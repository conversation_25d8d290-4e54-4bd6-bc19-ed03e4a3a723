# build_and_persist.py  (run in CI or a cron when TM table changes)
import psycopg2, ahocorasick, gzip, pickle, unicodedata, re, traceback
from IP.Trademarks_Bulk.trademark_db import get_db_connection

def update_trademarkindex():
    try: 
        conn = get_db_connection()
        with conn, conn.cursor() as c2:
            # Create the table if it doesn't exist
            c2.execute("""
                CREATE TABLE IF NOT EXISTS trademarks_precomputed_marks (
                    id INTEGER PRIMARY KEY,
                    built_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    automaton_blob OID
                );
            """)
            # Empty the table before inserting new data
            c2.execute("TRUNCATE TABLE trademarks_precomputed_marks;")
            conn.commit() # Commit the TRUNCATE operation

        # SQL command: fet list of trardmark text, without duplicates (keep the one with plaintiff_id, and the most recent one only)
        # Why plaintiff_id IS NULL? In Postgres, FALSE sorts before TRUE, so rows with a plaintiff (FALSE) win over those without.
        sql_query = """
        SELECT
            LOWER(mark_text) AS mark_text,
            plaintiff_id,
            reg_no,
            ser_no,
            applicant_name,
            int_cls
        FROM trademarks
        WHERE mark_text IS NOT NULL AND mark_text <> '';
        """
        # I removed AND mark_feature_code NOT IN (2, 3, 5);  which would only give the TEXT marks
        cur  = conn.cursor(name="tm_cur")     # server-side cursor, streams rows
        cur.execute(sql_query)

        A   = ahocorasick.Automaton()
        meta = {}                              # id → (normalized_mark_text, plaintiff_id, reg_no, ser_no, applicant_name, int_cls)

        norm = lambda s: re.sub(r'\s+', ' ', unicodedata.normalize('NFKC', s).lower()).strip()

        for idx, row in enumerate(cur):
            mark_text, plaintiff_id, reg_no, ser_no, applicant_name, int_cls = row
            text = norm(mark_text)
            A.add_word(text, idx)
            meta[idx] = (text, plaintiff_id, reg_no, ser_no, applicant_name, int_cls)

        A.make_automaton()

        blob = gzip.compress(pickle.dumps((A, meta), protocol=pickle.HIGHEST_PROTOCOL))

        # --- Changes for Large Object handling ---
        with conn: # Use conn as a context manager for transaction
            # Create a new large object
            lo = conn.lobject(0, 'wb') # 0 for new object, 'wb' for write binary
            oid = lo.oid # Get the OID of the new large object
            lo.write(blob) # Write the blob data to the large object
            lo.close() # Close the large object

            # Insert the OID into the table
            with conn.cursor() as c2:
                c2.execute("""
                    INSERT INTO trademarks_precomputed_marks (id, built_at, automaton_blob)
                    VALUES (1, now(), %s);
                """, (oid,)) # Pass the OID
            conn.commit() # Commit the INSERT operation
        # --- End of changes ---
    except Exception as e:
        if conn:
            conn.rollback()
        print(f"Error updating trademark index: {str(e)}, traceback: {traceback.format_exc()}")
        raise
        
        
if __name__ == "__main__":
    update_trademarkindex()