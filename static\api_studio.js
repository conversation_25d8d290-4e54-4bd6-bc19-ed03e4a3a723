// Initialize language on page load
function initializeLanguage() {
    const langSelect = document.getElementById('languageSelect');
    const currentLang = document.getElementById('language').value;
    langSelect.value = currentLang;
    langSelect.dispatchEvent(new Event('change'));
}

// Language support - moved outside DOMContentLoaded to be global
const i18n = {
    en: {
        home: 'Home',
        get_code: 'Get Code',
        input_form: 'Input Form',
        analysis_report: 'Analysis Report',
        api_key: 'API Key:',
        main_product_image_url: 'Main Product Image URL:',
        upload_main_image: 'Or Upload Main Product Image:',
        other_product_images: 'Other Product Images (Comma separated URLs):',
        upload_other_images: 'Or Upload Other Product Images:',
        ip_images: 'IP Images (Comma separated URLs):',
        upload_ip_images: 'Or Upload IP Images:',
        ip_keywords: 'IP Keywords (comma separated):',
        description: 'Description:',
        reference_text: 'Reference Text:',
        reference_images: 'Reference Images (comma separated URLs):',
        upload_reference_images: 'Or Upload Reference Images:',
        submit: 'Submit',
        clear_images: 'Clear selected images',
        check_id: 'Check ID:',
        risk_level: 'Risk Level:',
        risk_description: 'Risk Description:',
        type: 'Type:',
        ip_owner: 'IP Owner:',
        show_report: 'Show Report',
        hide_report: 'Hide Report',
        product_image: 'Product Image:',
        ip_image: 'IP Image:',
        processing: 'Please wait while your request is being processed...',
        python_code_title: 'Python API Call Code',
        copy_code: 'Copy Code to Clipboard',
        code_copied: 'Code Copied!',
        number_of_cases: 'Number of Cases',
        last_case_docket: 'Last Case Docket',
        last_case_date: 'Last Case Date',
        no_report: 'No report available.',
        running: 'Running',
        estimated_time: 'Estimated time: ',
        error_rate_limit: 'Rate limit exceeded. Please try again later.',
        error_invalid_api_key: 'Invalid API Key. Please check and try again.',
        error_server: 'Server error occurred. Please try again later or contact support.',
        error_network: 'Network error. Please check your connection and try again.',
        error_timeout: 'Request timed out. The server is still processing your request.',
        error_missing_field: 'Missing required field'
    },
    zh: {
        home: '首页',
        get_code: '获取代码',
        input_form: '输入表单',
        analysis_report: '分析报告',
        api_key: 'API密钥:',
        main_product_image_url: '主产品图片URL:',
        upload_main_image: '或上传主产品图片:',
        other_product_images: '其他产品图片(逗号分隔URL):',
        upload_other_images: '或上传其他产品图片:',
        ip_images: 'IP图片(逗号分隔URL):',
        upload_ip_images: '或上传IP图片:',
        ip_keywords: 'IP关键词(逗号分隔):',
        description: '产品描述:',
        reference_text: '参考文本:',
        reference_images: '参考图片(逗号分隔URL):',
        upload_reference_images: '或上传参考图片:',
        submit: '提交',
        clear_images: '清除已选图片',
        check_id: '检测ID:',
        risk_level: '风险等级:',
        risk_description: '风险描述:',
        type: '类型:',
        ip_owner: 'IP所有者:',
        show_report: '显示报告',
        hide_report: '隐藏报告',
        product_image: '产品图片:',
        ip_image: 'IP图片:',
        processing: '请等待，您的请求正在处理中...',
        python_code_title: 'Python API调用代码',
        copy_code: '复制代码到剪贴板',
        code_copied: '代码已复制!',
        number_of_cases: '案件数量',
        last_case_docket: '最新案件编号',
        last_case_date: '最新案件日期',
        no_report: '无可用报告。',
        running: '运行中',
        estimated_time: '预计时间：',
        error_rate_limit: '超出访问频率限制，请稍后再试。',
        error_invalid_api_key: 'API密钥无效，请检查后重试。',
        error_server: '服务器错误，请稍后再试或联系支持。',
        error_network: '网络错误，请检查您的连接后重试。',
        error_timeout: '请求超时，服务器仍在处理您的请求。',
        error_missing_field: '缺少必填字段'
    }
};

// --- Report Text Formatting Function ---
function formatReportText(originalText) {
    // 1) Remove double asterisks (but keep the text inside).
    originalText = originalText.replace(/\*\*(.*?)\*\*/g, '$1');

    // 2) Split into lines by actual newlines (\n).
    const lines = originalText.split('\n');

    let html = '';
    let currentIndent = 0; // how many <ul> we are "in"
    
    // Helper: count how many leading spaces are on a line
    function getIndentLevel(line) {
        const match = line.match(/^(\s*)/);  // capture leading whitespace
        const spaceCount = match ? match[1].length : 0;
        // each 4 spaces = next nesting level
        return Math.floor(spaceCount / 4);
    }

    // Loop over each line and decide what to do:
    for (let i = 0; i < lines.length; i++) {
        let line = lines[i];
        // If the line is completely blank, just skip it
        if (!line.trim()) {
            continue;
        }

        // figure out if it's a bullet line: starts with some indentation, then '*'
        const bulletMatch = line.match(/^(\s*)\*\s*(.*)$/);
        const indentLevel = getIndentLevel(line);

        // close or open <ul> tags as needed
        if (indentLevel > currentIndent) {
            // we're going deeper into nested lists
            for (let n = currentIndent; n < indentLevel; n++) {
                html += '<ul>';
            }
        } else if (indentLevel < currentIndent) {
            // we're coming back up
            for (let n = currentIndent; n > indentLevel; n--) {
                html += '</ul>';
            }
        }
        currentIndent = indentLevel;

        if (bulletMatch) {
            // It's a bullet line
            const bulletText = bulletMatch[2].trim();
            
            // Check for colon in bullet text
            const colonIndex = bulletText.indexOf(':');
            if (colonIndex !== -1) {
                const label = bulletText.substring(0, colonIndex + 1);
                const value = bulletText.substring(colonIndex + 1).trim();
                html += `<li><strong>${label}</strong> ${value}</li>`;
            } else {
                html += `<li>${bulletText}</li>`;
            }
        } else {
            // Handle section headers and colon-separated labels
            const sectionHeaderMatch = line.match(/^(\d+\.\s+.*)/);
            if (sectionHeaderMatch) {
                // Left-align section headers with no indentation
                html += `<p style="margin-left: 0; padding-left: 0;"><strong>${sectionHeaderMatch[1].trim()}</strong></p>`;
            } else {
                const colonIndex = line.indexOf(':');
                if (colonIndex !== -1) {
                    const label = line.substring(0, colonIndex + 1).trim();
                    const value = line.substring(colonIndex + 1).trim();
                    html += `<p><strong>${label}</strong> ${value}</p>`;
                } else {
                    html += `<p>${line.trim()}</p>`;
                }
            }
        }
    }

    // if we still have open <ul>, close them
    while (currentIndent > 0) {
        html += '</ul>';
        currentIndent--;
    }

    return html;
}

// Close overlay function
function closeImageOverlay() {
    const imageOverlay = document.getElementById('imageOverlay');
    if (imageOverlay) {
        imageOverlay.style.display = 'none';
        document.body.style.overflow = 'auto';
    }
}

// Function to validate file uploads
function validateFileUpload(id, previewId, max, name) {
    const input = document.getElementById(id),
        preview = previewId ? document.getElementById(previewId) : null,
        submitButton = document.getElementById("submitButton");
    
    if (input) {
        input.addEventListener("change", e => {
            if (e.target.files.length > max) {
                alert(`You can only upload up to ${max} images for ${name}.`);
                e.target.value = "";
                if (preview) preview.innerHTML = "";
                // Disable the submit button when the limit is exceeded
                submitButton.disabled = true;
            } else {
                // Re-enable the submit button if the file count is within limits
                submitButton.disabled = false;
            }
        });
    }
}

// Function to validate URL inputs
function validateUrlInput(id, max, name) {
    const input = document.getElementById(id);
    const submitButton = document.getElementById("submitButton");

    if (input) {
        input.addEventListener("blur", () => {
            if (input.value.trim()) {
                const urls = input.value.split(",").map(u => u.trim()).filter(Boolean);
                if (urls.length > max) {
                    alert(`You can only specify up to ${max} URLs for ${name}.`);
                    input.value = urls.slice(0, max).join(", ");
                    submitButton.disabled = true;
                } else {
                    // If the number of URLs is within range, enable the submit button.
                    submitButton.disabled = false;
                }
            } else {
                // Clear input is fine, so enable the submit button.
                submitButton.disabled = false;
            }
        });
    }
}

// Function to display results in the output div
function displayResults(result) {
    const outputDiv = document.getElementById('output');
    const isZh = document.getElementById('language').value === 'zh';
    
    // Clear previous content
    outputDiv.innerHTML = '';
    
    // Create report container
    const reportContainer = document.createElement('div');
    
    // Check ID display
    const checkIdDisplay = document.createElement('p');
    checkIdDisplay.className = 'check-id';
    checkIdDisplay.innerHTML = `<strong data-i18n="check_id"></strong> ${result.check_id || 'N/A'}`;
    reportContainer.appendChild(checkIdDisplay);

    // Summary section
    const summaryDiv = document.createElement('div');
    summaryDiv.className = 'result-summary';
    summaryDiv.innerHTML = `
        <p><strong data-i18n="risk_level"></strong> ${result.risk_level}</p>
    `;
    reportContainer.appendChild(summaryDiv);

    // Results cards
    if (result.results && Array.isArray(result.results)) {
        result.results.forEach((res, index) => {
            const card = document.createElement('div');
            card.className = 'result-card';

            // Risk level fallback
            const riskLevel = res.risk_level || result.risk_level || 'N/A';
            
            // Report handling
            let reportHtml = '<p data-i18n="no_report"></p>';
            if (res.report) {
                const reportEn = res.report_en || res.report;
                const reportZh = res.report || res.report_en;
                reportHtml = `
                    <button type="button" class="show-report-button" data-report-id="report-${index}">
                        ${isZh ? i18n.zh.show_report : i18n.en.show_report}
                    </button>
                    <div id="report-${index}" class="report-container" style="display: none;">
                        <div class="report-text" 
                                data-report-en="${encodeURIComponent(reportEn)}" 
                                data-report-zh="${encodeURIComponent(reportZh)}">
                            ${formatReportText(isZh ? reportZh : reportEn)}
                        </div>
                    </div>
                `;
            }

            // IP images handling
            let ipImagesHtml = '';
            // Handle different formats for backward compatibility
            if (res.ip_asset_urls) {
                // If it's a JSON string, parse it
                if (typeof res.ip_asset_urls === 'string' && res.ip_asset_urls[0] === '[') {
                    res.ip_asset_urls = JSON.parse(res.ip_asset_urls);
                } 
                // If it's a single string, wrap it in an array
                else if (typeof res.ip_asset_urls === 'string') {
                    res.ip_asset_urls = [res.ip_asset_urls];
                }
                
                // Now we can safely assume it's an array
                if (Array.isArray(res.ip_asset_urls)) {
                    ipImagesHtml = res.ip_asset_urls.map(filename => `
                        <img src="${filename}"
                                class="uniform-img">
                    `).join('');
                }
            }

            // Handle multiple product_url images
            let productImagesHtml = '';
            if (res.product_url && typeof res.product_url === 'string' && res.product_url[0] == '[') {
                res.product_url = JSON.parse(res.product_url);
            } 
            if (res.product_url && typeof res.product_url === 'string' && res.product_url[0] != '[') {
                res.product_url = [res.product_url];
            } 

            if (res.product_url && Array.isArray(res.product_url)) {
                productImagesHtml = res.product_url.map(url => `
                    ${url.startsWith('data:image') 
                        ? `<img src="data:image/jpeg;base64,${url.split(',')[1]}" class="uniform-img">`
                        : `<img src="${url}" class="uniform-img">`
                    }
                `).join('');
            }

            card.innerHTML = `
                <h3>${isZh ? '结果' : 'Result'} ${index + 1}</h3>
                <p><strong data-i18n="type"></strong> ${res.type}</p>
                <p><strong data-i18n="ip_owner"></strong> ${res.ip_owner}</p>
                <p><strong data-i18n="risk_level"></strong> ${riskLevel}</p>
                <p><strong data-i18n="risk_description"></strong> ${res.risk_description}</p>
                
                ${res.number_of_cases ? `
                <p><strong data-i18n="number_of_cases"></strong> ${res.number_of_cases}</p>` : ''}
                
                ${res.last_case_date_filed ? `
                <p><strong data-i18n="last_case_date"></strong> ${res.last_case_date_filed}</p>` : ''}
                
                ${res.last_case_docket ? `
                <p><strong data-i18n="last_case_docket"></strong> ${res.last_case_docket}</p>` : ''}
                
                ${ipImagesHtml ? `
                <p><strong data-i18n="ip_image"></strong><br>${ipImagesHtml}</p>` : ''}
                
                ${productImagesHtml ? `
                <p><strong data-i18n="product_image"></strong><br>${productImagesHtml}</p>` : ''}
                
                ${reportHtml}
            `;

            reportContainer.appendChild(card);
        });
    }

    outputDiv.appendChild(reportContainer);
    
    // Update translations for dynamic content
    document.querySelectorAll('[data-i18n]').forEach(el => {
        const key = el.dataset.i18n;
        el.textContent = i18n[document.getElementById('language').value][key];
    });
}

// Wrap all JavaScript initialization in a DOMContentLoaded event listener
document.addEventListener('DOMContentLoaded', function() {
    // Call language initialization
    initializeLanguage();
    
    // Main Product Image preview logic
    var fileInput = document.getElementById("main_product_image_upload");
    var urlInput = document.getElementById("main_product_image_url");
    var previewImage = document.getElementById("main_product_image_preview");
    var cancelButton = document.getElementById("cancel_preview");
    var uploadActiveInput = document.getElementById("main_product_image_upload_active");
    
    if (fileInput) {
        fileInput.addEventListener("change", function(e) {
            var file = e.target.files[0];
            if (file) {
                var reader = new FileReader();
                reader.onload = function(event) {
                    previewImage.src = event.target.result;
                    previewImage.style.display = "block";
                    cancelButton.style.display = "inline";
                    uploadActiveInput.value = "true";
                    urlInput.value = "";
                };
                reader.readAsDataURL(file);
            }
        });
    }
    
    if (cancelButton) {
        cancelButton.addEventListener("click", function() {
            fileInput.value = "";
            previewImage.style.display = "none";
            cancelButton.style.display = "none";
            uploadActiveInput.value = "false";
        });
    }
    
    // Initialize language switcher
    const languageSelect = document.getElementById('languageSelect');
    if (languageSelect) {
        languageSelect.addEventListener('change', function() {
            const lang = this.value;
            document.getElementById('language').value = lang;
            
            // Update UI elements
            document.querySelectorAll('[data-i18n]').forEach(el => {
                const key = el.dataset.i18n;
                el.textContent = i18n[lang][key];
            });
        
            // Update existing reports and toggle buttons
            document.querySelectorAll('.report-text').forEach(reportDiv => {
                const isZh = lang === 'zh';
                const rawText = decodeURIComponent(isZh ? reportDiv.dataset.reportZh : reportDiv.dataset.reportEn);
                reportDiv.innerHTML = formatReportText(rawText);
            });
        
            // Update show/hide report button texts
            document.querySelectorAll('.show-report-button').forEach(button => {
                const reportId = button.dataset.reportId;
                const reportContainer = document.getElementById(reportId);
                if (reportContainer) {
                    button.textContent = reportContainer.style.display === 'none' 
                        ? i18n[lang].show_report 
                        : i18n[lang].hide_report;
                }
            });
        });
    }
    
    // Prevent submitting both file and URL for main product image
    const checkForm = document.getElementById("checkForm");
    if (checkForm) {
        checkForm.addEventListener("submit", function(e) {
            if (uploadActiveInput.value === "true" && urlInput.value.trim() !== "") {
                alert("Please either upload a file OR provide a URL, not both.");
                e.preventDefault();
                return;
            }
            if (uploadActiveInput.value === "false") {
                fileInput.value = "";
            }
        });
    
        // Main submission logic with spinner and temporary message
        checkForm.addEventListener("submit", function(e) {
            e.preventDefault();
            
            // Disable the submit button to prevent duplicate submissions
            var submitButton = this.querySelector("button[type='submit']");
            submitButton.disabled = true;
            
            // Display a spinner and processing message
            var outputDiv = document.getElementById("output");
            outputDiv.innerHTML = `
                <div class="info-message">
                    <div style='display:flex; align-items:center; gap:10px;'>
                        <div class='spinner'></div>
                        <span data-i18n='processing'></span>
                    </div>
                </div>`;
            
            // Trigger language update immediately after creating the message
            document.querySelector('[data-i18n="processing"]').textContent = i18n[document.getElementById("language").value].processing;
    
            // Immediate scroll to top with header offset
            const header = document.querySelector('.header');
            const headerHeight = header ? header.offsetHeight : 0;
            
            // New approach: Use documentElement scroll with CSS scroll-padding
            document.documentElement.style.scrollPaddingTop = `${headerHeight}px`;
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
    
            // Gather form values
            var api_key = document.getElementById("api_key").value;
            var mainProductImageValue = (uploadActiveInput.value === "true") ? previewImage.src : urlInput.value.trim();
            
            // For other product images
            var other_product_images;
            if (document.getElementById("other_product_images_upload_active").value === "true") {
                other_product_images = otherProductDataUrls;
            } else {
                other_product_images = document.getElementById("other_product_images_url").value.split(",").map(item => item.trim()).filter(item => item);
            }
            
            // For IP images, use the data URLs if uploaded
            var ip_images = (ipUploadActiveInput.value === "true") 
                                ? ipDataUrls 
                                : document.getElementById("ip_images_url").value.split(",").map(item => item.trim()).filter(item => item);
            
            var ip_keywords = document.getElementById("ip_keywords").value.split(",").map(item => item.trim()).filter(item => item);
            var description = document.getElementById("description").value;
            var reference_text = document.getElementById("reference_text").value;
            // For Reference images, use the data URLs if uploaded
            var reference_images = (document.getElementById("reference_images_upload_active").value === "true") 
                                    ? referenceDataUrls 
                                    : document.getElementById("reference_images").value.split(",").map(item => item.trim()).filter(item => item);
            
            var data = {
                api_key: api_key,
                main_product_image: mainProductImageValue,
                other_product_images: other_product_images,
                ip_images: ip_images,
                ip_keywords: ip_keywords,
                description: description,
                reference_text: reference_text,
                reference_images: reference_images,
                language: document.getElementById("language").value
            };
    
            // Add timeout controller
            const controller = new AbortController();
            const timeoutDuration = 600000; // 10 minutes
            const timeoutId = setTimeout(() => controller.abort(), timeoutDuration);
    
            // Make the API call
            api_enpoint = "http://localhost:5000/check_api"
            // api_enpoint = "https://api.maidalv.com/check_api"
            fetch(api_enpoint, {
                method: "POST",
                headers: {"Content-Type": "application/json"},
                body: JSON.stringify(data),
                signal: controller.signal
            })
            .then(response => {
                clearTimeout(timeoutId);
                // Add explicit error handling based on HTTP status codes
                if (!response.ok) {
                    const language = document.getElementById("language").value;
                    const errorMessages = {
                        401: i18n[language].error_invalid_api_key,
                        429: i18n[language].error_rate_limit,
                        500: i18n[language].error_server
                    };
                    
                    // Get appropriate error message or use a generic one
                    const errorMessage = errorMessages[response.status] || 
                                       `Error ${response.status}: ${response.statusText}`;
                    
                    // Try to get more details from the response JSON
                    return response.json().then(errorJson => {
                        throw new Error(`${errorMessage} - ${errorJson.message || errorJson.error || ''} ${errorJson.error_code ? `(${errorJson.error_code})` : ''}`);
                    }).catch(e => {
                        // If we couldn't parse JSON, just throw the original error
                        if (e.message && e.message.includes('JSON')) {
                            throw new Error(errorMessage);
                        }
                        throw e; // Otherwise, rethrow the error with the additional details
                    });
                }
                if (!response.headers.get("content-type").includes("application/json")) {
                    throw new Error(`Invalid content type: ${response.headers.get("content-type")}`);
                }
                return response.json();
            })
            .then(initialResponse => {
                if (!initialResponse.check_id) throw new Error('Missing check ID in response');
                
                // If the response indicates an error, handle it
                if (initialResponse.status === 'failed' || initialResponse.error) {
                    const errorMessage = initialResponse.error || 'An unknown error occurred';
                    throw new Error(errorMessage);
                }
                
                // Display initial queue position and estimated time if available
                if (initialResponse.status === 'processing') {                        
                    let estimatedTimeMessage = initialResponse.estimated_completion_time !== undefined
                        ? `${i18n[document.getElementById("language").value].estimated_time}: ${initialResponse.estimated_completion_time}`
                        : '';

                    outputDiv.innerHTML = `
                        <div class="info-message">
                            <div style='display:flex; align-items:center; gap:10px;'>
                                <div class='spinner'></div>
                                <span data-i18n='running'></span>
                                <span>${estimatedTimeMessage}</span>
                            </div>
                        </div>`;
                    document.querySelector('[data-i18n="running"]').textContent = i18n[document.getElementById("language").value].running;
                }
                
                const pollInterval = 5000; // 5 seconds
                const maxPolls = 60; // 5 minutes total
                let polls = 0;
                
                const poller = setInterval(() => {
                    fetch(`/check_status/${initialResponse.check_id}`)
                    .then(response => {
                        // Add explicit error handling for status endpoint too
                        if (!response.ok) {
                            const language = document.getElementById("language").value;
                            const errorMessages = {
                                401: i18n[language].error_invalid_api_key,
                                429: i18n[language].error_rate_limit,
                                500: i18n[language].error_server,
                                404: response.statusText // Use status text for not found errors
                            };
                            
                            const errorMessage = errorMessages[response.status] || 
                                                `Error ${response.status}: ${response.statusText}`;
                            
                            // If the fetch returned an error, we'll still try to extract the JSON
                            // to get more details from the API
                            return response.json().then(errorJson => {
                                throw new Error(`${errorMessage} - ${errorJson.message || errorJson.error || ''}`);
                            }).catch(e => {
                                // If we couldn't get JSON, just throw the original error
                                throw new Error(errorMessage);
                            });
                        }
                        return response.json();
                    })
                    .then(statusResponse => {
                        console.log("Full JSON response:", statusResponse);
                        
                        if (statusResponse.status === 'error') {
                            // Handle error status in a successful response
                            clearInterval(poller);
                            const language = document.getElementById("language").value;
                            const errorMessage = statusResponse.message || i18n[language].error_server;
                            const errorCode = statusResponse.error_code || '';
                            
                            outputDiv.innerHTML = `
                                <div class="error-message">
                                    <strong>Error</strong>
                                    <p>${errorMessage}</p>
                                    ${errorCode ? `<div class="error-code">${errorCode}</div>` : ''}
                                </div>
                            `;
                            submitButton.disabled = false;
                            return;
                        }
                        
                        if (statusResponse.status === 'completed') {
                            clearInterval(poller);
                            // Process results as before
                            displayResults(statusResponse.result);
                            submitButton.disabled = false; 
                        } else if (statusResponse.status === 'processing') {
                            // Update queue position and estimated time
                            let estimatedTimeMessage = statusResponse.estimated_completion_time !== undefined
                                ? `${i18n[document.getElementById("language").value].estimated_time} ${statusResponse.estimated_completion_time}`
                                : '';

                            outputDiv.innerHTML = `
                                <div class="info-message">
                                    <div style='display:flex; align-items:center; gap:10px;'>
                                        <div class='spinner'></div>
                                        <span data-i18n='processing'></span>
                                        <span>${estimatedTimeMessage}</span>
                                    </div>
                                </div>`;
                            document.querySelector('[data-i18n="processing"]').textContent = i18n[document.getElementById("language").value].processing;
                            // Update the dynamic text (queue position and estimated time)
                            document.querySelectorAll('[data-i18n]').forEach(elem => {
                                const key = elem.getAttribute('data-i18n');
                                if (i18n[document.getElementById("language").value][key]) {
                                    elem.textContent = i18n[document.getElementById("language").value][key];
                                }
                            });
                        }
                        if (polls++ >= maxPolls) {
                            clearInterval(poller);
                            throw new Error('Analysis timed out');
                        }
                    })
                    .catch(error => {
                        // Clear interval if there's an error to prevent continued polling
                        clearInterval(poller);
                        
                        const language = document.getElementById("language").value;
                        let errorMessage = error.message;
                        let errorCode = '';
                        
                        // Specialized error messages
                        if (error.name === 'AbortError') {
                            errorMessage = i18n[language].error_timeout;
                            errorCode = 'TIMEOUT';
                        } else if (error.message.includes('NetworkError')) {
                            errorMessage = i18n[language].error_network;
                            errorCode = 'NETWORK_ERROR';
                        } else if (error.message.includes('Missing required field')) {
                            // Extract the specific field name from the error message
                            const fieldMatch = error.message.match(/Missing required field: (.+)( - .*)?$/);
                            const missingField = fieldMatch && fieldMatch[1] ? fieldMatch[1] : '';
                            errorMessage = `${i18n[language].error_missing_field}: ${missingField}`;
                            errorCode = 'MISSING_FIELD';
                        }
                        
                        // Extract error code if it's in the message
                        const codeMatch = errorMessage.match(/\(([A-Z_]+)\)$/);
                        if (codeMatch && codeMatch[1]) {
                            errorCode = codeMatch[1];
                            // Remove the code from the displayed message
                            errorMessage = errorMessage.replace(/\s*\([A-Z_]+\)$/, '');
                        }
                        
                        outputDiv.innerHTML = `
                            <div class="error-message">
                                <strong>Error</strong>
                                <p>${errorMessage}</p>
                                ${errorCode ? `<div class="error-code">${errorCode}</div>` : ''}
                            </div>
                        `;
                        
                        submitButton.disabled = false;
                    });
                }, pollInterval);
            })
            .catch(error => {
                clearTimeout(timeoutId);
                const language = document.getElementById("language").value;
                
                let errorMessage = error.message;
                let errorCode = '';
                
                // Specialized error messages
                if (error.name === 'AbortError') {
                    errorMessage = i18n[language].error_timeout;
                    errorCode = 'TIMEOUT';
                } else if (error.message.includes('NetworkError')) {
                    errorMessage = i18n[language].error_network;
                    errorCode = 'NETWORK_ERROR';
                } else if (error.message.includes('Missing required field')) {
                    // Extract the specific field name from the error message
                    const fieldMatch = error.message.match(/Missing required field: (.+)( - .*)?$/);
                    const missingField = fieldMatch && fieldMatch[1] ? fieldMatch[1] : '';
                    errorMessage = `${i18n[language].error_missing_field}: ${missingField}`;
                    errorCode = 'MISSING_FIELD';
                }
                
                // Extract error code if it's in the message
                const codeMatch = errorMessage.match(/\(([A-Z_]+)\)$/);
                if (codeMatch && codeMatch[1]) {
                    errorCode = codeMatch[1];
                    // Remove the code from the displayed message
                    errorMessage = errorMessage.replace(/\s*\([A-Z_]+\)$/, '');
                }
                
                // Display the error with better formatting using our new CSS classes
                outputDiv.innerHTML = `
                    <div class="error-message">
                        <strong>Error</strong>
                        <p>${errorMessage}</p>
                        ${errorCode ? `<div class="error-code">${errorCode}</div>` : ''}
                    </div>
                `;
                
                // Re-enable submit button even on error
                submitButton.disabled = false;
            });
        });
    }
    
    // Other Product Images functionality - updated to store the Data URLs
    var otherProductDataUrls = []; // Global array to store data URLs for other product images
    
    var otherFilesInput = document.getElementById("other_product_images_upload");
    var otherUrlInput = document.getElementById("other_product_images_url");
    var otherPreviewsDiv = document.getElementById("other_product_images_previews");
    var cancelOtherButton = document.getElementById("cancel_other_product_images");
    var otherUploadActiveInput = document.getElementById("other_product_images_upload_active");
    
    if (otherFilesInput) {
        otherFilesInput.addEventListener("change", function(e) {
            otherProductDataUrls = []; // reset the array on change
            otherPreviewsDiv.innerHTML = "";
            var files = e.target.files;
            if(files.length > 0) {
                otherUploadActiveInput.value = "true";
                otherUrlInput.value = "";
                cancelOtherButton.style.display = "inline";
                for(var i = 0; i < files.length; i++){
                    (function(file){
                        var reader = new FileReader();
                        reader.onload = function(event) {
                            var result = event.target.result;
                            otherProductDataUrls.push(result); // store the data URL
                            var img = document.createElement("img");
                            img.src = result;
                            img.style.maxWidth = "100px";
                            img.style.maxHeight = "100px";
                            img.style.marginRight = "5px";
                            img.style.marginBottom = "5px";
                            otherPreviewsDiv.appendChild(img);
                        };
                        reader.readAsDataURL(file);
                    })(files[i]);
                }
            }
        });
    }
    
    if (cancelOtherButton) {
        cancelOtherButton.addEventListener("click", function() {
            otherFilesInput.value = "";
            otherPreviewsDiv.innerHTML = "";
            otherUploadActiveInput.value = "false";
            cancelOtherButton.style.display = "none";
        });
    }
    
    // -----------------------------
    // IP Images functionality
    // Declare a global array for storing IP image data URLs
    var ipDataUrls = [];

    var ipFilesInput = document.getElementById("ip_images_upload");
    var ipUrlInput = document.getElementById("ip_images_url");
    var ipPreviewsDiv = document.getElementById("ip_images_previews");
    var cancelIpButton = document.getElementById("cancel_ip_images");
    var ipUploadActiveInput = document.getElementById("ip_images_upload_active");

    if (ipFilesInput) {
        ipFilesInput.addEventListener("change", function(e) {
            ipDataUrls = []; // reset the array on change
            ipPreviewsDiv.innerHTML = "";
            var files = e.target.files;
            if(files.length > 0){
                ipUploadActiveInput.value = "true";
                ipUrlInput.value = "";
                cancelIpButton.style.display = "inline";
                for(var i = 0; i < files.length; i++){
                    (function(file){
                        var reader = new FileReader();
                        reader.onload = function(event) {
                            var result = event.target.result;
                            ipDataUrls.push(result);  // collect each data URL
                            var img = document.createElement("img");
                            img.src = result;
                            img.style.maxWidth = "100px";
                            img.style.maxHeight = "100px";
                            img.style.marginRight = "5px";
                            img.style.marginBottom = "5px";
                            ipPreviewsDiv.appendChild(img);
                        };
                        reader.readAsDataURL(file);
                    })(files[i]);
                }
            }
        });
    }

    if (cancelIpButton) {
        cancelIpButton.addEventListener("click", function() {
            ipFilesInput.value = "";
            ipPreviewsDiv.innerHTML = "";
            ipUploadActiveInput.value = "false";
            cancelIpButton.style.display = "none";
        });
    }
    
    // Reference Images functionality
    // Global array to store data URLs for reference images
    var referenceDataUrls = [];

    var referenceFilesInput = document.getElementById("reference_images_upload");
    var referencePreviewsDiv = document.getElementById("reference_images_previews");
    var cancelReferenceButton = document.getElementById("cancel_reference_images");
    var referenceUploadActiveInput = document.getElementById("reference_images_upload_active");

    if (referenceFilesInput) {
        referenceFilesInput.addEventListener("change", function(e) {
            referenceDataUrls = []; // Reset the array on new selection
            referencePreviewsDiv.innerHTML = "";
            var files = e.target.files;
            if (files.length > 0) {
                referenceUploadActiveInput.value = "true";
                // Loop through selected files and show previews
                for (var i = 0; i < files.length; i++) {
                    (function(file) {
                        var reader = new FileReader();
                        reader.onload = function(event) {
                            var result = event.target.result;
                            referenceDataUrls.push(result); // Store the data URL
                            var img = document.createElement("img");
                            img.src = result;
                            img.style.maxWidth = "150px";
                            img.style.marginRight = "10px";
                            img.style.marginTop = "10px";
                            referencePreviewsDiv.appendChild(img);
                        };
                        reader.readAsDataURL(file);
                    })(files[i]);
                }
                cancelReferenceButton.style.display = "inline";
            }
        });
    }

    if (cancelReferenceButton) {
        cancelReferenceButton.addEventListener("click", function() {
            referenceFilesInput.value = "";
            referencePreviewsDiv.innerHTML = "";
            referenceUploadActiveInput.value = "false";
            cancelReferenceButton.style.display = "none";
        });
    }
    
    // Additional validation for form submission
    if (checkForm) {
        checkForm.addEventListener("submit", function(e) {
            var otherUploadActive = otherUploadActiveInput.value;
            var otherUrl = otherUrlInput.value.trim();
            if(otherUploadActive === "true" && otherUrl !== "") {
                alert("Please either upload Other Product Images OR provide URLs, not both.");
                e.preventDefault();
                return;
            }
            var ipUploadActive = ipUploadActiveInput.value;
            var ipUrl = ipUrlInput.value.trim();
            if(ipUploadActive === "true" && ipUrl !== "") {
                alert("Please either upload IP Images OR provide URLs, not both.");
                e.preventDefault();
                return;
            }
            if(otherUploadActive === "false") {
                otherFilesInput.value = "";
            }
            if(ipUploadActive === "false") {
                ipFilesInput.value = "";
            }
        });
    }
    
    // Modal functionality
    // var modal = document.getElementById("apiCodeModal");
    // var getCodeButton = document.getElementById("getCodeButton");
    // var closeModalButton = document.getElementById("closeModalButton");
    // var copyCodeButton = document.getElementById("copyCodeButton");

//     if (getCodeButton) {
//         getCodeButton.addEventListener("click", function(e) {
//             e.preventDefault(); // Prevent form submission

//             // Gather form values (similar to submit function)
//             var api_key = document.getElementById("api_key").value;
//             var mainProductImageValue = (uploadActiveInput.value === "true") ? previewImage.src : urlInput.value.trim();
//             var other_product_images;
//             if (document.getElementById("other_product_images_upload_active").value === "true") {
//                 other_product_images = otherProductDataUrls;
//             } else {
//                 other_product_images = document.getElementById("other_product_images_url").value.split(",").map(item => item.trim()).filter(item => item);
//             }
//             var ip_images = (ipUploadActiveInput.value === "true")
//                                 ? ipDataUrls
//                                 : document.getElementById("ip_images_url").value.split(",").map(item => item.trim()).filter(item => item);
//             var ip_keywords = document.getElementById("ip_keywords").value.split(",").map(item => item.trim()).filter(item => item);
//             var description = document.getElementById("description").value;
//             var reference_text = document.getElementById("reference_text").value;
//             var reference_images = (document.getElementById("reference_images_upload_active").value === "true") 
//                                     ? referenceDataUrls 
//                                     : document.getElementById("reference_images").value.split(",").map(item => item.trim()).filter(item => item);

//             var data = {
//                 "main_product_image": mainProductImageValue,
//                 "other_product_images": other_product_images,
//                 "ip_images": ip_images,
//                 "ip_keywords": ip_keywords,
//                 "description": description,
//                 "reference_text": reference_text,
//                 "reference_images": reference_images,
//                 language: document.getElementById("language").value
//             };

//             // Construct the Python API call code as a string
//             var pythonApiCallCode = `import json
// import requests
// import base64
// import time

// def file_to_base64(file_path):
//     """
//     Convert a file to its Base64 string representation.
//     """
//     with open(file_path, "rb") as f:
//         return base64.b64encode(f.read()).decode("utf-8")

// def send_api_request_with_backoff(url, headers, json_payload, max_retries=5, delay=1):
//     """
//     Send a POST request to the API endpoint with the provided JSON payload.
//     Uses an exponential backoff strategy between retries (capped at 60 seconds).
//     """
//     for attempt in range(max_retries):
//         try:
//             response = requests.post(url, headers=headers, json=json_payload)
//             response.raise_for_status()
//             return response
//         except Exception as e:
//             if attempt < max_retries - 1:
//                 backoff_delay = min(delay * (2 ** attempt), 60)  # exponential backoff, capped at 60 seconds
//                 print(f"Error: {e}")
//                 print(f"Retry {attempt + 1}/{max_retries}: Waiting {backoff_delay:.2f} seconds before retrying...")
//                 time.sleep(backoff_delay)
//             else:
//                 print(f"Call failed after {max_retries} attempts: {e}")
//                 raise

// def main():
//     # Gather input from the user. You can also hard-code values if preferred.
//     print("Please provide the following details:")
//     api_key = input("Enter API Key: ")
//     main_product_image_url = input("Enter main product image URL (leave blank if uploading a file): ")
//     main_product_file = input("Enter file path for main product image upload (leave blank if providing URL): ")
//     other_product_images_url = input("Enter comma separated URLs for other product images: ")
//     ip_images_url = input("Enter comma separated URLs for IP images: ")
//     ip_keywords = input("Enter IP keywords (comma separated): ")
//     description = input("Enter description: ")
//     reference_text = input("Enter reference text: ")
//     reference_images = input("Enter comma separated URLs for reference images: ")
//     language = input("Enter language (default en): ") or "en"

//     # Create the JSON payload.
//     formJSON = {
//         "api_key": api_key,
//         "main_product_image": main_product_image_url if main_product_image_url else "",
//         "other_product_images": other_product_images_url.split(',') if other_product_images_url else [],
//         "ip_images": ip_images_url.split(',') if ip_images_url else [],
//         "ip_keywords": ip_keywords.split(',') if ip_keywords else [],
//         "description": description,
//         "reference_text": reference_text,
//         "reference_images": reference_images.split(',') if reference_images else [],
//         "language": language
//     }

//     # Override with file upload if provided
//     if main_product_file:
//         try:
//             formJSON["main_product_image"] = file_to_base64(main_product_file)
//         except Exception as e:
//             print("Error converting the file to Base64:", e)
//             return  # Exit if file conversion fails

//     print("\\nJSON Payload to be sent:")
//     print(json.dumps(formJSON, indent=4))

//     # Set the API endpoint URL. Replace <your_public_ip> with the actual IP address or domain.
//     api_endpoint = "http://127.0.0.1:5000/check_api"
//     headers = {"Content-Type": "application/json"}

//     # Send the API request with exponential backoff.
//     try:
//         response = send_api_request_with_backoff(api_endpoint, headers, formJSON)
//         data = response.json()
//         print("\\nAPI Response:")
//         print(json.dumps(data, indent=4))
//     except Exception as e:
//         print("\\nError sending API request:", e)

// if __name__ == "__main__":
//     main()
// `;

//             // Display the Python API call code in the modal
//             var pythonCodeSnippet = document.getElementById("pythonApiCodeSnippet");
//             pythonCodeSnippet.textContent = pythonApiCallCode.trim();

//             // Show the modal
//             modal.style.display = "block";
//         });
//     }

    // if (closeModalButton) {
    //     closeModalButton.addEventListener("click", function() {
    //         modal.style.display = "none";
    //     });
    // }

    // When user clicks outside of the modal, close it
    // window.addEventListener("click", function(event) {
    //     if (event.target == modal) {
    //         modal.style.display = "none";
    //     }
    // });

    // if (copyCodeButton) {
    //     copyCodeButton.addEventListener("click", function() {
    //         // Get the text content from the python code snippet element
    //         var pythonCodeSnippet = document.getElementById("pythonApiCodeSnippet");
    //         var codeToCopy = pythonCodeSnippet.textContent;

    //         // Use the Clipboard API to copy the text
    //         navigator.clipboard.writeText(codeToCopy).then(function() {
    //             // Provide feedback to the user that the code has been copied
    //             copyCodeButton.textContent = "Code Copied!";
    //             setTimeout(function() {
    //                 copyCodeButton.textContent = "Copy Code to Clipboard"; // Revert text back
    //             }, 2000); // Revert back to "Copy Code to Clipboard" after 2 seconds
    //         }, function(err) {
    //             console.error("Failed to copy code: ", err);
    //             copyCodeButton.textContent = "Copy Failed"; // Indicate copy failure
    //             setTimeout(function() {
    //                 copyCodeButton.textContent = "Copy Code to Clipboard"; // Revert text back
    //             }, 2000); // Revert back after 2 seconds
    //         });
    //     });
    // }

    // --- Report Toggle Functionality ---
    document.addEventListener('click', function(event) {
        if (event.target.classList.contains('show-report-button')) {
            const reportId = event.target.dataset.reportId;
            const reportContainer = document.getElementById(reportId);
            if (reportContainer) {
                const isZh = document.getElementById("language").value === 'zh';
                reportContainer.style.display = reportContainer.style.display === 'none' ? 'block' : 'none';
                event.target.textContent = reportContainer.style.display === 'block' 
                    ? (isZh ? '隐藏报告' : 'Hide Report') 
                    : (isZh ? '显示报告' : 'Show Report');
            }
        }
    });

    // Image overlay click handler
    const imageOverlay = document.getElementById('imageOverlay');
    const expandedImage = document.getElementById('expandedImage');
    const closeOverlay = document.querySelector('.close-overlay');

    if (document.getElementById('output')) {
        document.getElementById('output').addEventListener('click', function(e) {
            if (e.target.tagName === 'IMG') {
                expandedImage.src = e.target.src;
                imageOverlay.style.display = 'flex';
                document.body.style.overflow = 'hidden'; // Prevent scrolling
            }
        });
    }

    // Close overlay
    if (imageOverlay) {
        imageOverlay.addEventListener('click', function(e) {
            if (e.target === imageOverlay || e.target === closeOverlay) {
                closeImageOverlay();
            }
        });
    }

    // Close with ESC key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && imageOverlay.style.display === 'flex') {
            closeImageOverlay();
        }
    });

    // Validation for file uploads
    validateFileUpload("ip_images_upload", "ip_images_previews", 3, "IP Images");
    validateFileUpload("reference_images_upload", "reference_images_previews", 3, "Reference Images");
    validateFileUpload("other_product_images_upload", "other_product_images_previews", 4, "Other Product Images");

    // URL input validation
    validateUrlInput("ip_images_url", 3, "IP Images");
    validateUrlInput("reference_images", 3, "Reference Images");
    validateUrlInput("other_product_images_url", 4, "Other Product Images");

    // Add this new function to handle results display
    function displayResults(result) {
        const outputDiv = document.getElementById('output');
        const isZh = document.getElementById('language').value === 'zh';
        
        // Clear previous content
        outputDiv.innerHTML = '';
        
        // Create report container
        const reportContainer = document.createElement('div');
        
        // Check ID display
        const checkIdDisplay = document.createElement('p');
        checkIdDisplay.className = 'check-id';
        checkIdDisplay.innerHTML = `<strong data-i18n="check_id"></strong> ${result.check_id || 'N/A'}`;
        reportContainer.appendChild(checkIdDisplay);

        // Summary section
        const summaryDiv = document.createElement('div');
        summaryDiv.className = 'result-summary';
        summaryDiv.innerHTML = `
            <p><strong data-i18n="risk_level"></strong> ${result.risk_level}</p>
        `;
        reportContainer.appendChild(summaryDiv);

        // Results cards
        if (result.results && Array.isArray(result.results)) {
            result.results.forEach((res, index) => {
                const card = document.createElement('div');
                card.className = 'result-card';

                // Risk level fallback
                const riskLevel = res.risk_level || result.risk_level || 'N/A';
                
                // Report handling
                let reportHtml = '<p data-i18n="no_report"></p>';
                if (res.report) {
                    const reportEn = res.report_en || res.report;
                    const reportZh = res.report || res.report_en;
                    reportHtml = `
                        <button type="button" class="show-report-button" data-report-id="report-${index}">
                            ${isZh ? i18n.zh.show_report : i18n.en.show_report}
                        </button>
                        <div id="report-${index}" class="report-container" style="display: none;">
                            <div class="report-text" 
                                    data-report-en="${encodeURIComponent(reportEn)}" 
                                    data-report-zh="${encodeURIComponent(reportZh)}">
                                ${formatReportText(isZh ? reportZh : reportEn)}
                            </div>
                        </div>
                    `;
                }

                // IP images handling
                let ipImagesHtml = '';
                // Handle different formats for backward compatibility
                if (res.ip_asset_urls) {
                    // If it's a JSON string, parse it
                    if (typeof res.ip_asset_urls === 'string' && res.ip_asset_urls[0] === '[') {
                        res.ip_asset_urls = JSON.parse(res.ip_asset_urls);
                    } 
                    // If it's a single string, wrap it in an array
                    else if (typeof res.ip_asset_urls === 'string') {
                        res.ip_asset_urls = [res.ip_asset_urls];
                    }
                    
                    // Now we can safely assume it's an array
                    if (Array.isArray(res.ip_asset_urls)) {
                        ipImagesHtml = res.ip_asset_urls.map(filename => `
                            <img src="${filename}" class="uniform-img">
                        `).join('');
                    }
                }

                // Handle multiple product_url images
                let productImagesHtml = '';
                if (res.product_url && typeof res.product_url === 'string' && res.product_url[0] == '[') {
                    res.product_url = JSON.parse(res.product_url);
                } 
                if (res.product_url && typeof res.product_url === 'string' && res.product_url[0] != '[') {
                    res.product_url = [res.product_url];
                } 

                if (res.product_url && Array.isArray(res.product_url)) {
                    productImagesHtml = res.product_url.map(url => `
                        ${url.startsWith('data:image') 
                            ? `<img src="data:image/jpeg;base64,${url.split(',')[1]}" class="uniform-img">`
                            : `<img src="${url}" class="uniform-img">`
                        }
                    `).join('');
                }

                card.innerHTML = `
                    <h3>${isZh ? '结果' : 'Result'} ${index + 1}</h3>
                    <p><strong data-i18n="type"></strong> ${res.type}</p>
                    <p><strong data-i18n="ip_owner"></strong> ${res.ip_owner}</p>
                    <p><strong data-i18n="risk_level"></strong> ${riskLevel}</p>
                    <p><strong data-i18n="risk_description"></strong> ${res.risk_description}</p>
                    
                    ${res.number_of_cases ? `
                    <p><strong data-i18n="number_of_cases"></strong> ${res.number_of_cases}</p>` : ''}
                    
                    ${res.last_case_date_filed ? `
                    <p><strong data-i18n="last_case_date"></strong> ${res.last_case_date_filed}</p>` : ''}
                    
                    ${res.last_case_docket ? `
                    <p><strong data-i18n="last_case_docket"></strong> ${res.last_case_docket}</p>` : ''}
                    
                    ${ipImagesHtml ? `
                    <p><strong data-i18n="ip_image"></strong><br>${ipImagesHtml}</p>` : ''}
                    
                    ${productImagesHtml ? `
                    <p><strong data-i18n="product_image"></strong><br>${productImagesHtml}</p>` : ''}
                    
                    ${reportHtml}
                `;

                reportContainer.appendChild(card);
            });
        }

        outputDiv.appendChild(reportContainer);
        
        // Update translations for dynamic content
        document.querySelectorAll('[data-i18n]').forEach(el => {
            const key = el.dataset.i18n;
            el.textContent = i18n[document.getElementById('language').value][key];
        });
    }
});