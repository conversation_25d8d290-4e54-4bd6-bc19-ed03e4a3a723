# IP/Trademarks/trademark_parser.py

import os
import multiprocessing
import platform
import atexit
import json
from datetime import datetime
# Removed: import logging
from typing import List, Dict, Any, Tuple
from lxml import etree as ET
from Common.cpu_count import get_allocated_cpus
from logdata import log_message # Added logdata import

# Removed logging configuration block
# logger = logging.getLogger(__name__) # Removed logger initialization


# --- Multiprocessing Pool for XML Parsing ---

# Global pool management variables
_xml_parser_pool = None
_xml_parser_pool_pid = None
_xml_parser_num_processes = None

def init_xml_worker():
    """Initializer for the XML parser worker processes (if needed)."""
    # log_message(f"Initializing XML parser worker (PID: {os.getpid()})", level='DEBUG') # Use log_message if needed
    pass

def get_xml_parser_pool():
    """Creates or returns the existing multiprocessing pool for XML parsing."""
    global _xml_parser_pool, _xml_parser_pool_pid, _xml_parser_num_processes
    _xml_parser_num_processes = get_allocated_cpus()
    current_pid = os.getpid()
    if _xml_parser_pool is None or _xml_parser_pool_pid != current_pid:
        shutdown_xml_parser_pool() # Ensure any old pool is closed

        context_method = 'spawn' if platform.system() == "Windows" else 'fork'
        ctx = multiprocessing.get_context(context_method)

        _xml_parser_pool = ctx.Pool(
            processes=_xml_parser_num_processes,
            initializer=init_xml_worker
        )
        _xml_parser_pool_pid = current_pid
        log_message(f"Created XML parser pool with {_xml_parser_num_processes} processes (PID: {current_pid}, Context: {context_method})", level='INFO')
    return _xml_parser_pool

def shutdown_xml_parser_pool():
    """Shuts down the XML parser multiprocessing pool."""
    global _xml_parser_pool, _xml_parser_pool_pid
    if _xml_parser_pool is not None:
        log_message(f"Shutting down XML parser pool (PID: {_xml_parser_pool_pid})", level='INFO')
        try:
            _xml_parser_pool.close()
            _xml_parser_pool.join()
            log_message("XML parser pool shut down successfully.", level='INFO')
        except Exception as e:
            log_message(f"Error shutting down XML parser pool: {e}", level='ERROR')
        finally:
             _xml_parser_pool = None

# Register the shutdown function to be called on script exit
atexit.register(shutdown_xml_parser_pool)
# --- End Multiprocessing Pool ---


# --- XML Parsing Helper Functions ---

def get_element_text(element, xpath):
    """
    Helper function to safely extract text from an XML element (works with lxml).
    """
    found = element.find(xpath)
    return found.text.strip() if found is not None and found.text else None

def parse_xml_chunk(xml_file_path: str, start_index: int, end_index: int, source: str) -> Tuple[List[Dict[str, Any]], List[str]]:
    """
    Parses a specific chunk (range of elements) of the trademark XML file using lxml.
    Designed to be run in a separate process.
    """
    # Using log_message directly now
    trademarks_chunk: List[Dict[str, Any]] = []
    serials_chunk: List[str] = []
    current_index = 0
    processed_in_chunk = 0
    context = None
    
    #https://www.uspto.gov/news/og/con/files/cons181.htm
    codes_to_skip = ["600", "601", "602", "603", "604", "605", "606", "607", "608", "609","614","618", "620", "622", "626", "710", "711", "712", "713", "714", "900"]  

    try:
        context = ET.iterparse(xml_file_path, events=('end',), tag='case-file', recover=True)

        for _, elem in context:
            if current_index < start_index:
                current_index += 1
                elem.clear()
                while elem.getprevious() is not None: del elem.getparent()[0]
                continue
            if current_index >= end_index:
                elem.clear()
                while elem.getprevious() is not None: del elem.getparent()[0]
                break

            trademark: Dict[str, Any] = {}
            try:
                # --- Processing Logic (same as before) ---
                trademark['reg_no'] = get_element_text(elem, './/registration-number')
                trademark['ser_no'] = get_element_text(elem, './/serial-number')
                if not trademark['ser_no']:
                    print(f"⚠️ Worker {os.getpid()}: Case-file at index {current_index} is missing serial number.")
                trademark['TRO'] = None
                trademark['image_source'] = None

                case_file_header = elem.find('.//case-file-header')
                mark_feature_code = None
                if case_file_header is not None:
                    mark_status_code_str = get_element_text(case_file_header, './/status-code')
                    if mark_status_code_str.strip() in codes_to_skip:
                        # If the status code is in our skip list, we ignore this trademark.
                        continue
                    if mark_status_code_str:
                        try: trademark['mark_current_status_code'] = int(mark_status_code_str)
                        except ValueError: trademark['mark_current_status_code'] = None
                        
                    trademark['mark_text'] = get_element_text(case_file_header, './/mark-identification')
                    
                    filing_date_str = get_element_text(case_file_header, './/filing-date')
                    if filing_date_str:
                        try: trademark['filing_date'] = datetime.strptime(filing_date_str, '%Y%m%d').date().isoformat()
                        except ValueError: trademark['filing_date'] = None
                        
                    mark_feature_code_str = get_element_text(case_file_header, './/mark-drawing-code')
                    if mark_feature_code_str:
                        try:
                            mark_feature_code = int(mark_feature_code_str)
                            trademark['mark_feature_code'] = mark_feature_code
                        except ValueError: trademark['mark_feature_code'] = None
                    else: trademark['mark_feature_code'] = None
                    
                    std_char_indicator = get_element_text(case_file_header, './/standard-characters-claimed-in')
                    if std_char_indicator: trademark['mark_standard_character_indicator'] = std_char_indicator == 'T'

                serial_number = trademark.get('ser_no')
                if serial_number: # Download all images
                    serials_chunk.append(serial_number)

                highest_entry_num = -1
                highest_owner = None
                for owner in elem.findall('.//case-file-owner'):
                    entry_num_str = get_element_text(owner, './/entry-number')
                    if entry_num_str:
                        try:
                            entry_num = int(entry_num_str)
                            if entry_num > highest_entry_num:
                                highest_entry_num = entry_num
                                highest_owner = owner
                        except ValueError: pass
                if highest_owner is not None:
                    trademark['applicant_name'] = get_element_text(highest_owner, './/party-name')
                    country_codes = []
                    for nationality in highest_owner.findall('.//nationality'):
                        country = get_element_text(nationality, './/country')
                        if country and country not in country_codes: country_codes.append(country)
                    trademark['country_codes'] = country_codes if country_codes else None

                int_cls = []
                for classification in elem.findall('.//classifications/classification'):
                    int_code = get_element_text(classification, './/international-code')
                    if int_code:
                        try:
                            if int_code not in ["A", "B"] and int_code not in int_cls: int_cls.append(int(int_code))
                        except ValueError: pass
                trademark['int_cls'] = int_cls if int_cls else None

                nb_suits = sum(1 for ev in elem.findall('.//case-file-event-statements/case-file-event-statement') if get_element_text(ev, './/code') == 'NOSUI')
                trademark['nb_suits'] = nb_suits

                goods_services = []
                for classification in elem.findall('.//classifications/classification'):
                    gs_item = {}
                    int_code = get_element_text(classification, './/international-code')
                    if int_code:
                        try: gs_item['NiceClass'] = int(int_code)
                        except ValueError: gs_item['NiceClass'] = None
                    gs_item['StatusCode'] = get_element_text(classification, './/status-code')
                    gs_item['FirstDateUsed'] = get_element_text(classification, './/first-use-anywhere-date')
                    gs_item['FirstUsedInCommerceDate'] = get_element_text(classification, './/first-use-in-commerce-date')
                    gs_item['subclass'] = None
                    if gs_item.get('NiceClass') or gs_item.get('StatusCode'): goods_services.append(gs_item)
                trademark['goods_services'] = json.dumps(goods_services) if goods_services else None

                mark_disclaimer_text_daily = []
                mark_image_colour_statement_daily = []
                case_file_statements_other = []
                goods_services_text_parts = []
                for statement in elem.findall('.//case-file-statements/case-file-statement'):
                    type_code = get_element_text(statement, './/type-code')
                    text = get_element_text(statement, './/text')
                    if type_code and text:
                        if type_code in ['D0', 'D1']: mark_disclaimer_text_daily.append(text)
                        elif type_code in ['CC', 'CD']: mark_image_colour_statement_daily.append(text)
                        elif type_code == 'TR': trademark['mark_translation_statement_daily'] = text
                        elif type_code == 'N0': trademark['name_portrait_statement_daily'] = text
                        elif type_code == 'DM': trademark['mark_description_statement_daily'] = text
                        elif type_code == 'CS': trademark['certification_mark_statement_daily'] = text
                        elif type_code == 'LS': trademark['lining_stippling_statement_daily'] = text
                        elif type_code == 'TF': trademark['section_2f_statement_daily'] = text
                        elif type_code.startswith('GS'): goods_services_text_parts.append(text)
                        else: case_file_statements_other.append({'type_code': type_code, 'text': text})
                trademark['mark_disclaimer_text_daily'] = mark_disclaimer_text_daily or None
                trademark['mark_image_colour_statement_daily'] = mark_image_colour_statement_daily or None
                trademark['case_file_statements_other'] = json.dumps(case_file_statements_other) if case_file_statements_other else None
                trademark['goods_services_text_daily'] = ' '.join(goods_services_text_parts) if goods_services_text_parts else None

                latest_date = None
                latest_event = None
                highest_number = -1
                for event in elem.findall('.//case-file-event-statements/case-file-event-statement'):
                    date_str = get_element_text(event, './/date')
                    number_str = get_element_text(event, './/number')
                    if date_str:
                        try:
                            event_date = datetime.strptime(date_str, '%Y%m%d').date()
                            event_number = int(number_str) if number_str else 0
                            if latest_date is None or event_date > latest_date or (event_date == latest_date and event_number > highest_number):
                                latest_date = event_date
                                highest_number = event_number
                                latest_event = event
                        except ValueError: pass
                if latest_event is not None:
                    trademark['latest_event_description_daily'] = get_element_text(latest_event, './/description-text')

                trademark['associated_marks'] = None
                trademark['mark_disclaimer_text'] = None
                trademark['mark_image_colour_claimed_text'] = None
                trademark['mark_image_colour_part_claimed_text'] = None
                trademark['national_design_code'] = None
                trademark['mark_current_status_external_description_text'] = None
                trademark['last_updated_source'] = source
                # --- End Processing Logic ---

                if trademark.get('ser_no'):
                    trademarks_chunk.append(trademark)
                    processed_in_chunk += 1
                else:
                    log_message(f"Worker {os.getpid()}: Skipped case-file at index {current_index} due to missing serial number.", level='WARNING')

            except Exception as e:
                ser_no = trademark.get('ser_no', 'UNKNOWN')
                log_message(f"Worker {os.getpid()}: Error processing SN {ser_no} at index {current_index}: {e}", level='ERROR', exc_info=True)

            finally:
                elem.clear()
                current_index += 1
                while elem.getprevious() is not None:
                    del elem.getparent()[0]

        return trademarks_chunk, serials_chunk

    except (ET.XMLSyntaxError, ET.ParseError) as e:
        log_message(f"Worker {os.getpid()}: Fatal XML ParseError in chunk {start_index}-{end_index}: {e}", level='ERROR')
        return [], []
    except Exception as e:
        log_message(f"Worker {os.getpid()}: Unexpected fatal error in chunk {start_index}-{end_index}: {e}", level='ERROR')
        return [], []
    finally:
        del context

# --- Orchestrator Function ---

# Define fixed chunk size (can be moved to constants or config later)
FIXED_CHUNK_SIZE = 5000

def _generate_chunk_indices(xml_file_path: str, chunk_size: int):
    """
    Generates (start_index, end_index) tuples for chunks without full pre-counting.
    Iterates through the XML efficiently using iterparse.
    """
    log_message(f"Generating chunk indices for {xml_file_path} with chunk size {chunk_size}...", level='INFO')
    start_index = 0
    count_in_chunk = 0
    context = None
    total_processed = 0
    try:
        context = ET.iterparse(xml_file_path, events=('end',), tag='case-file', recover=True)
        for _, elem in context:
            count_in_chunk += 1
            total_processed += 1
            if count_in_chunk == chunk_size:
                end_index = start_index + count_in_chunk
                # log_message(f"Yielding chunk: {start_index} - {end_index}", level='DEBUG')
                yield (start_index, end_index)
                start_index = end_index
                count_in_chunk = 0
            # Memory clearing
            elem.clear()
            while elem.getprevious() is not None:
                del elem.getparent()[0]

        # Yield the last partial chunk if any elements were processed
        if count_in_chunk > 0:
            end_index = start_index + count_in_chunk
            log_message(f"Yielding final chunk: {start_index} - {end_index}", level='DEBUG')
            yield (start_index, end_index)
        log_message(f"Finished generating chunk indices. Total elements processed by generator: {total_processed}")
        if total_processed == 0:
             log_message(f"No case-file elements found during chunk generation in {xml_file_path}.", level='WARNING')

    except (ET.XMLSyntaxError, ET.ParseError) as e:
        log_message(f"XML ParseError during chunk generation in {xml_file_path}: {e}", level='ERROR')
        # Decide how to handle: raise, return, or yield nothing? Yielding nothing seems safest.
    except Exception as e:
        log_message(f"Unexpected error during chunk generation in {xml_file_path}: {e}", level='ERROR')
    finally:
        del context # Help GC

# Wrapper function needed because pool.imap passes only one argument
def _parse_xml_chunk_wrapper(args):
    """Unpacks arguments and calls the actual parsing function."""
    xml_file_path, start_index, end_index, source = args
    return parse_xml_chunk(xml_file_path, start_index, end_index, source)


def parse_daily_trademark_xml_parallel(xml_file_path: str, source: str) -> Tuple[List[Dict[str, Any]], List[str]]:
    """
    Orchestrates the parallel parsing of a trademark XML file using fixed-size chunks.
    Generates chunk indices on-the-fly, uses multiprocessing pool with imap (preserves order),
    and aggregates results.
    """
    log_message(f"Starting parallel XML processing for {xml_file_path} using fixed chunks of size {FIXED_CHUNK_SIZE}", level='INFO')

    pool = get_xml_parser_pool()
    trademarks = []
    serials_to_download_with_duplicates = []
    total_trademarks_aggregated = 0
    processed_any_chunks = False

    try:
        # Create an iterator of arguments for the worker wrapper
        # The generator yields (start_index, end_index)
        chunk_indices_generator = _generate_chunk_indices(xml_file_path, FIXED_CHUNK_SIZE)
        worker_args_iterator = (
            (xml_file_path, start, end, source) for start, end in chunk_indices_generator
        )

        # Use pool.imap to process chunks in parallel while preserving order
        # The results will come back in the order the chunks were generated
        results_iterator = pool.imap(_parse_xml_chunk_wrapper, worker_args_iterator)

        log_message(f"Processing chunks with {_xml_parser_num_processes} processes using pool.imap...", level='INFO')

        for chunk_trademarks, chunk_serials in results_iterator:
            processed_any_chunks = True
            if chunk_trademarks: # Check if the chunk processing returned any trademarks
                trademarks.extend(chunk_trademarks)
                serials_to_download_with_duplicates.extend(chunk_serials)
                total_trademarks_aggregated += len(chunk_trademarks)
                # Optional: Log progress periodically
                if total_trademarks_aggregated % (FIXED_CHUNK_SIZE * 5) == 0: # Log every 5 chunks worth
                    log_message(f"Aggregated {total_trademarks_aggregated} trademarks so far...", level='DEBUG')
            elif chunk_serials: # Handle case where only serials might be returned (unlikely based on current logic but safe)
                serials_to_download_with_duplicates.extend(chunk_serials)

        if not processed_any_chunks:
             log_message(f"No chunks were processed for {xml_file_path}. This might indicate an empty file or an issue during chunk generation.", level='WARNING')

    except Exception as e:
        log_message(f"Error occurred during pool.imap execution or aggregation: {e}", level='ERROR', exc_info=True)
        # Return empty lists on error
        return [], []

    finally:
        # Force garbage collection of any remaining XML references
        pool._cache.clear()  # Clear the pool's cache
        import gc
        gc.collect()  # Force garbage collection

    log_message("Parallel parsing finished. Aggregating results...", level='INFO')

    # Remove duplicates from serials while trying to preserve order
    serials_to_download = list(dict.fromkeys(serials_to_download_with_duplicates))
    print(f"✅ Aggregated {len(trademarks)} trademarks and {len(serials_to_download)} unique serials for image download.")

    return trademarks, serials_to_download
