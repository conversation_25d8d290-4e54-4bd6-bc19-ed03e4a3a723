# Use an ARM-compatible base image
FROM python:3.12-bullseye
    
ENV DEBIAN_FRONTEND=noninteractive

# Install system & GUI dependencies
RUN apt-get update && apt-get install -y \
    tesseract-ocr \
    libtesseract-dev \
    libleptonica-dev \
    pkg-config \
    poppler-utils \
    libgl1 \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    wget \
    gnupg \
    build-essential \
    default-libmysqlclient-dev \
    default-mysql-client \
    unzip \
    libaio1 \    
    curl \
    xvfb \
    libxi6 \
    libgconf-2-4 \
    libnss3 \
    libxss1 \
    libappindicator3-1 \
    libasound2 \
    libatk-bridge2.0-0 \
    libgtk-3-0 \
    libgbm1 \
    x11-utils \
    fonts-dejavu \
    fonts-liberation \
    fonts-freefont-ttf \
    locales \
    net-tools \  
    redis-server \
    apt-transport-https \
    ca-certificates \
    tmux \ 
    openssh-client \
    git


# Install SSH server
RUN apt-get install -y openssh-server \
    && mkdir /var/run/sshd \
    && echo 'root:trosdc2024' | chpasswd \
    && sed -i 's/#PermitRootLogin prohibit-password/PermitRootLogin yes/' /etc/ssh/sshd_config \
    && sed -i 's/#PasswordAuthentication yes/PasswordAuthentication yes/' /etc/ssh/sshd_config \
    && sed -i 's/#ListenAddress 0.0.0.0/ListenAddress 0.0.0.0/' /etc/ssh/sshd_config \ 
    && sed -i 's/#Port 22/Port 2223/' /etc/ssh/sshd_config \
    && apt-get clean && rm -rf /var/lib/apt/lists/*

# Install Google Cloud CLI
RUN echo "deb [signed-by=/usr/share/keyrings/cloud.google.gpg] https://packages.cloud.google.com/apt cloud-sdk main" | tee -a /etc/apt/sources.list.d/google-cloud-sdk.list && curl https://packages.cloud.google.com/apt/doc/apt-key.gpg | gpg --dearmor -o /usr/share/keyrings/cloud.google.gpg && apt-get update -y && apt-get install google-cloud-cli -y
    
# Set timezone & locale
ENV TZ=America/New_York
ENV LANG=en_US.UTF-8
ENV LANGUAGE=en_US:en
ENV LC_ALL=en_US.UTF-8
RUN sed -i -e "s/# $LANG.*/$LANG UTF-8/" /etc/locale.gen && \
    dpkg-reconfigure --frontend=noninteractive locales && \
    update-locale LANG=$LANG

# Install Google Chrome
RUN wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | apt-key add - && \
    echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" \
    > /etc/apt/sources.list.d/google-chrome.list && \
    apt-get update && apt-get install -y google-chrome-stable && \
    rm -rf /var/lib/apt/lists/*
ENV CHROME_BIN=/usr/bin/google-chrome
# ENV CHROME_DRIVER=/usr/bin/chromedriver

# Set the working directory
WORKDIR /app
# ENV PYTHONPATH=/app:$PYTHONPATH 
ENV PYTHONPATH=/app 
# By appending :$PYTHONPATH, we are preserving any existing paths that were already in the PYTHONPATH variable.

# Copy requirements.txt first, so that if the code has changed, the dependencies are not re-installed (different stage)
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt


# Copy the application code
COPY . .

# Set strict permissions for the private key
# RUN chmod 600 /app/.ssh/id_rsa && \
# chmod 700 /app/.ssh
    
# Set ownership to root (or whichever user is running SSH commands)
# RUN chown -R root:root /app/.ssh

# Create directories for the chrome profile and database
RUN mkdir -p /app/data/chrome_user_data
RUN mkdir -p /app/data/db

# Copy the chromedriver to a directory where it can be modified by undetected_chromedriver
RUN mkdir -p /root/.local/share/undetected_chromedriver
RUN cp /usr/bin/google-chrome /root/.local/share/undetected_chromedriver/chromedriver_copy
RUN chmod +x /root/.local/share/undetected_chromedriver/chromedriver_copy

# Expose the port 5000 for flask and 2223 for SSH and 5555 for Flower
EXPOSE 5000 2223 5555

# Run the application
# CMD ["python", "app.py"]
RUN chmod +x start.sh
# CMD ["./start.sh"]
CMD ["xvfb-run", "-a", "--server-args=-screen 0 1920x1080x24", "./start.sh"]