#nofloqa
import json, os, sys, mimetypes, shutil
from PIL import Image # Pillow imports (ExifTags not needed)
import piexif # Added piexif import for EXIF manipulation
import glob
import json
import traceback
sys.path.append(os.getcwd())
from AI.GC_VertexAI import vertex_genai_image_gen_async
from AI.GCV_GetImageParts import get_image_parts_async
import Common.Constants as Constants
from langfuse import observe
import langfuse
from Scraper import ChineseWebsites_C_copyright_tineye, ChineseWebsites_C_copyright_google_vision
from Scraper.ChineseWebsites_C_copyright_image_deduplicator import deduplicate_images
from logdata import log_message
from typing import List, Dict, Any

# ❌⚠️📥🔥✅☑️✔️💯💧⛏🔨🗝🔑 🔒💡⛔🚫❗


# Step C: process the copyrighted images

@observe()
async def C_copyright_process_picture(
    c_input_item: Dict[str, Any], # The dictionary containing all context for this item
    copyright_case_base_dir: str,
    watermarked_dir, reverse_search_output_dir, reverse_search_final_dir,
    duplicate_dir: str # This is the duplicate_dir specific to C_step processing
) -> List[Dict[str, str]]:
    """
    Processes a single image (original or split part).
    Copies the image from download_path, adds metadata,
    removes watermark using GenAI (or uses reverse search), saves clean version to copyright_case_base_dir.
    """
    # Extract necessary fields from c_input_item
    reg_no: str = c_input_item["reg_no"]
    img_url: str = c_input_item["img_url"]
    source_page_url: str = c_input_item["source_page_url"]
    source_site: str = c_input_item["source_site"]
    watermark_description: str = c_input_item["watermark_description"]
    download_path: str = c_input_item["download_path"] # Path from copyright_cn_allpictures_dir

    try:
        # download_path is the image from copyright_cn_allpictures_dir (already deduplicated globally) => We first copy it to watermarked_dir.
        original_extension = os.path.splitext(download_path)[-1]
        watermark_path_in_c_step = os.path.join(watermarked_dir, f"{Constants.sanitize_name(reg_no)}_original{original_extension}")
        shutil.copy(download_path, watermark_path_in_c_step)

        # Convert to JPG if needed (for EXIF)
        watermark_path_in_c_step = convert_to_jpg(watermark_path_in_c_step, reg_no)
        file_extension = os.path.splitext(watermark_path_in_c_step)[-1] # Get extension after potential conversion

        # Add metadata
        add_metadata(watermark_path_in_c_step, source_page_url, source_site, img_url)

        base_name = Constants.sanitize_name(reg_no)

        # Create reg_no specific subdirectories for this reverse search instance
        # This ensures that the results of one reverse search don't interfere with another.
        current_reverse_search_output_dir = os.path.join(reverse_search_output_dir, base_name)
        os.makedirs(current_reverse_search_output_dir, exist_ok=True)
        current_reverse_search_final_dir = os.path.join(reverse_search_final_dir, base_name)
        os.makedirs(current_reverse_search_final_dir, exist_ok=True)

        # Google Vision reverse image search
        log_message(f"C_step: Performing Google Vision reverse image search for {watermark_path_in_c_step}")
        reverse_search_results = ChineseWebsites_C_copyright_google_vision.reverse_search_with_google_vision(
            watermark_path_in_c_step,
            output_folder=current_reverse_search_output_dir,   # Use scoped output directory
            final_folder=current_reverse_search_final_dir,     # Use scoped final directory
            max_images=3,similarity_threshold=0.3
        )

        # If Google Vision found something we take it
        if len(reverse_search_results) > 0:
            reverse_search_results.sort(key=lambda x: x.get("similarity", 0.3), reverse=True)
            high_similarity_image_path = reverse_search_results[0].get("path")
            dest_filename = f"{base_name}_GoogleVision{os.path.splitext(high_similarity_image_path)[1]}"
            dest_path = os.path.join(copyright_case_base_dir, dest_filename)
            shutil.copy2(high_similarity_image_path, dest_path)
            c_input_item["final_image_path"] = dest_path
            c_input_item["method"] = "GoogleVision"

        else: # If google vision found nothing, we use google
            log_message(f"👀 C_step: Performing TinEye reverse image search for {watermark_path_in_c_step}")
            reverse_search_results = ChineseWebsites_C_copyright_tineye.reverse_search_on_tineye( # Result not directly used, relies on file output
                watermark_path_in_c_step,
                output_folder=current_reverse_search_output_dir,   # Use scoped output directory
                final_folder=current_reverse_search_final_dir,     # Use scoped final directory
                max_images=3
            )
            
            # If TinEYE found something we take it
            if len(reverse_search_results) > 0:
                reverse_search_results.sort(key=lambda x: x.get("similarity", 0.0), reverse=True)
                high_similarity_image_path = reverse_search_results[0].get("path")
                dest_filename = f"{base_name}_TinEye{os.path.splitext(high_similarity_image_path)[1]}"
                dest_path = os.path.join(copyright_case_base_dir, dest_filename)
                shutil.copy2(high_similarity_image_path, dest_path)
                c_input_item["final_image_path"] = dest_path
                c_input_item["method"] = "TinEye"

            else: # If google and tinEye found nothing good, use GenAI
                # Use GenAI for watermark removal
                log_message(f"      💡 Reverse search for {base_name} did not yield a match > 0.4 (score: ....) => Using GenAI for watermark removal.")
                watermark_removal_prompt = f"Your job is to generate a picture exactly the same but remove the {watermark_description}"

                # Check if a GenAI version already exists in the final directory (copyright_case_base_dir)
                genai_pattern = os.path.join(copyright_case_base_dir, f"{base_name}_genai*")
                existing_genai_in_final_dir = [
                    f for f in glob.glob(genai_pattern)
                    if os.path.isfile(f) and os.path.dirname(f) == os.path.abspath(copyright_case_base_dir)
                ]

                if existing_genai_in_final_dir:
                    log_message(f"      C_step: Found existing GenAI processed image for {reg_no}: {existing_genai_in_final_dir[0]}")
                    c_input_item["final_image_path"] = existing_genai_in_final_dir[0]
                else:
                    inline_data = await vertex_genai_image_gen_async(
                        [("text", watermark_removal_prompt), ("image_path", watermark_path_in_c_step)]
                    )
                    if inline_data and not isinstance(inline_data, str):
                        processed_image_bytes = inline_data.data
                        mime_type = inline_data.mime_type
                        genai_extension = mimetypes.guess_extension(mime_type) or f".{mime_type.split('/')[-1]}"
                        final_image_path_genai = os.path.join(copyright_case_base_dir, f"{base_name}_GenAI{genai_extension}")

                        try:
                            with open(final_image_path_genai, "wb") as f:
                                f.write(processed_image_bytes)
                            log_message(f"      C_step: Saved watermark-removed image for {reg_no} to {final_image_path_genai}")
                            c_input_item["final_image_path"] = final_image_path_genai
                            c_input_item["method"] = "GenAI"
                        except Exception as save_err:
                            log_message(f"      C_step: Error saving watermark-removed image for {reg_no}: {save_err}")
                            # Fallback: if GenAI fails, use original image
                            orig_final_path = os.path.join(copyright_case_base_dir, f"{base_name}_original{file_extension}")
                            # Check if original already exists in final_dir
                            if not os.path.exists(orig_final_path):
                                shutil.copy2(watermark_path_in_c_step, orig_final_path)
                                log_message(f"      C_step: Fallback to original image: {orig_final_path}")
                            else:
                                log_message(f"      C_step: Fallback to Original image already exists in final directory: {orig_final_path}")
                            c_input_item["final_image_path"] = orig_final_path
                            c_input_item["method"] = "Original"
                    else:
                        log_message(f"      C_step: GenAI did not return image data for {reg_no}. Using original image as fallback.")
                        orig_final_path = os.path.join(copyright_case_base_dir, f"{base_name}_original{file_extension}")
                        if not os.path.exists(orig_final_path):
                            shutil.copy2(watermark_path_in_c_step, orig_final_path)
                            log_message(f"      C_step: Fallback to original image: {orig_final_path}")
                        else:
                            log_message(f"      C_step: Original image already exists in final directory: {orig_final_path}")
                        c_input_item["final_image_path"] = orig_final_path
                        c_input_item["method"] = "Original"

        # Fallback if no paths were added (e.g., all processing failed or skipped due to existing files)
        if "final_image_path" not in c_input_item:
            log_message(f"      C_step: No processed images generated for {reg_no}, using original from watermarked_dir as fallback.")
            orig_final_path = os.path.join(copyright_case_base_dir, f"{base_name}_original{file_extension}")
            if not os.path.exists(orig_final_path):
                shutil.copy2(watermark_path_in_c_step, orig_final_path)
            else:
                 log_message(f"      C_step: Original image (fallback) already exists in final directory: {orig_final_path}")
            c_input_item["final_image_path"] = orig_final_path

        
        log_message(f"      C_step: Final images for {reg_no}: {c_input_item["final_image_path"]}")
        return c_input_item

    except Exception as e:
        log_message(f"      C_step: Error during C_copyright_process_picture for {reg_no}: {e}, {traceback.format_exc()}", level="ERROR")
        langfuse.get_client().update_current_span(
            metadata={"Error": str(e), "Traceback": traceback.format_exc()}
        )
        # Clean up potentially incomplete original download if it exists and an error occurred
        if 'watermark_path_in_c_step' in locals() and os.path.exists(watermark_path_in_c_step) and 'final_image_path_genai' not in locals() and 'dest_path' not in locals() : # Only delete if processing failed before final save
             try:
                 os.remove(watermark_path_in_c_step)
                 log_message(f"      C_step: Removed incomplete watermarked file: {watermark_path_in_c_step}")
             except OSError as rm_err:
                 log_message(f"      C_step: Error removing incomplete watermarked file {watermark_path_in_c_step}: {rm_err}")

        # In case of error, try to return original image if available
        if 'watermark_path_in_c_step' in locals() and os.path.exists(watermark_path_in_c_step):
            dest_filename_err = f"{Constants.sanitize_name(reg_no)}_error_original{os.path.splitext(watermark_path_in_c_step)[1]}"
            dest_path_err = os.path.join(copyright_case_base_dir, dest_filename_err)
            try:
                # Only copy if it doesn't already exist
                if not os.path.exists(dest_path_err):
                    shutil.copy2(watermark_path_in_c_step, dest_path_err)
                    log_message(f"      C_step: Error recovery: saved original image to {dest_path_err}")
                else:
                    log_message(f"      C_step: Error recovery: original image already exists at {dest_path_err}")
                return [{"reg": reg_no, "image_path": dest_path_err}]
            except Exception as copy_err:
                log_message(f"      C_step: Error during recovery copy: {copy_err}")
        
        return None


def convert_to_jpg(watermark_path, reg_no):
    # 1. Check image format and convert if necessary before adding metadata
    try:
        with Image.open(watermark_path) as img:
            img_format = img.format.upper() # Get format (JPEG, PNG, etc.)
            if img_format not in ['JPEG', 'TIFF']:
                print(f"Original format {img_format} not supported by piexif. Converting {reg_no} to JPEG.")
                # Ensure image is in RGB mode for JPEG saving
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # Create new path for JPEG version
                base, _ = os.path.splitext(watermark_path)
                new_jpeg_path = f"{base}.jpg"
                
                # Save as JPEG
                img.save(new_jpeg_path, "JPEG")
                print(f"Saved converted JPEG to: {new_jpeg_path}")
                
                # Remove original non-JPEG/TIFF file
                try:
                    if watermark_path != new_jpeg_path:
                        os.remove(watermark_path)
                except OSError as rm_err:
                    log_message(f"Warning: Could not remove original file {watermark_path} after conversion: {rm_err}")
                    
                # Update path to point to the new JPEG file
                watermark_path = new_jpeg_path
            # If no conversion was done, the original path is still valid.
            return watermark_path
                
    except FileNotFoundError:
            log_message(f"      Error: Downloaded file not found at {watermark_path} before format check.")
            raise # Re-raise the error to be caught by the outer try-except
    except Exception as img_err:
        log_message(f"      Error during image format check/conversion for {reg_no}: {img_err}")
        raise # Re-raise to be caught by outer try-except

def add_metadata(watermark_path, source_page_url, source_site, img_url):
    # 2. Add Metadata to Original File (now guaranteed to be JPEG/TIFF or skipped)
    metadata = {
        "source_site": source_site,
        "source_page_url": source_page_url,
        "original_image_url": img_url,
        "processing_step": "original_download" # Indicate this is the original
    }
    metadata_json = json.dumps(metadata)
    try:
        # Load original image's EXIF, add comment, insert back into file
        exif_dict = piexif.load(watermark_path)
        comment_bytes = metadata_json.encode('utf-8')
        exif_dict["Exif"][piexif.ExifIFD.UserComment] = comment_bytes
        exif_bytes = piexif.dump(exif_dict)
        piexif.insert(exif_bytes, watermark_path) # Insert EXIF into the original file
        # log_message(f"Added metadata to original file: {watermark_path}")
    except Exception as meta_err:
        # If EXIF loading/insertion fails (e.g., non-JPEG), we skip adding metadata but continue
        log_message(f"Warning: Could not add metadata to original file {watermark_path}: {meta_err}")
