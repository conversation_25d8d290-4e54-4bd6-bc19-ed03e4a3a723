from Alerts.ReprocessCases import reprocess_cases
from Alerts.LexisNexis.Scrape_Date_Range import scrape_date_range
from datetime import date, timedelta

def daily_fetch():
    start_date = date.today() - timedelta(days=1)
    end_date = date.today()
    new_cases_df = scrape_date_range(start_date, end_date)
    
    processing_options = {
        'update_steps': True, # Forces update of case steps from source
        'process_pictures': True, # Process images from PDFs, always true for now!
        'upload_files_nas': True, # Upload files to NAS
        'upload_files_cos': True, # Upload files to COS
        'run_plaintiff_overview': True, # Run AI plaintiff overview
        'run_summary_translation': True, # Run AI summary translation
        'run_step_translation': True, # Run AI step translation
        'save_to_db': True, # Save results to DB
        'processing_mode': 'full_reprocess', # Processing mode: 'full_reprocess' (default) or 'resume'
        'refresh_days_threshold': 15 # Refresh threshold in days
    }
    reprocess_cases(cases_to_reprocess=new_cases_df, processing_options=processing_options, trace_name="Daily Fetch Workflow")
    
if __name__ == '__main__':
    daily_fetch()