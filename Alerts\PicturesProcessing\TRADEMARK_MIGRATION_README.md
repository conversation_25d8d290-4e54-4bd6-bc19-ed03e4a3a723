# Trademark Filename Migration

This document describes the migration from reg_no based filenames to ser_no based filenames for trademark images.

## Overview

The migration changes trademark image filenames from:
- `{reg_no}.webp` → `{ser_no}.webp`
- `{reg_no}_full.webp` → `{ser_no}_full.webp`

Where `ser_no` is always 8 digits (e.g., `87654321.webp`).

## Files Modified

### 1. Migration Script
- **`migrate_trademark_filenames.py`** - Main migration script

### 2. Processing Files Updated
- **`Trademarks_RegNo.py`** - Updated `process_trademark_data()` to use ser_no based naming
- **`Trademarks_ByName.py`** - Updated filename generation to use standard ser_no format
- **`Trademarks_Exhibits.py`** - Updated to handle ser_no based naming when available

### 3. Test Script
- **`test_migration.py`** - Test script to validate migration logic

## Migration Process

The migration script (`migrate_trademark_filenames.py`) performs the following steps:

1. **Load Cases**: Retrieves all cases with trademark images from the database
2. **Check Migration Status**: Skips files already in ser_no format (8 digits)
3. **Database Lookup**: Attempts to find ser_no using reg_no from trademark database
4. **LLM Assistance**: If database lookup fails, uses AI to identify reg_no and ser_no from certificate images
5. **Manual Input**: If AI fails, prompts user for manual ser_no input with option to skip
6. **Database Update**: Updates trademark records with `tro='TRUE'` and adds plaintiff_id to plaintiff_ids array
7. **File Copying**: Uses `async_copy_file_with_retry` to copy files to new ser_no based names
8. **Dataframe Update**: Updates case dataframe with new filenames and ser_no information

## Usage

### Running the Migration

```bash
cd /path/to/USSideRefactoring
python Alerts/PicturesProcessing/migrate_trademark_filenames.py
```

### Running Tests

```bash
python Alerts/PicturesProcessing/test_migration.py
```

## Database Schema

The migration interacts with the `trademarks` table:

```sql
-- Key fields used:
reg_no TEXT          -- Registration number (6-7 digits)
ser_no TEXT          -- Serial number (8 digits) 
tro TEXT             -- Set to 'TRUE' for migrated trademarks
plaintiff_ids BIGINT[] -- Array of plaintiff IDs
```

## File Naming Convention

### Before Migration
- Exhibit files: `{docket}_page{N}_0.webp`, `{docket}_page{N}_0_full.webp`
- RegNo files: `{docket}_regno_{reg_no}.webp`, `{docket}_regno_{reg_no}_full.webp`  
- ByName files: `{ser_no}_tm.webp`, `{ser_no}_cert.webp`

### After Migration
- All files: `{ser_no}.webp`, `{ser_no}_full.webp`
- Where `ser_no` is exactly 8 digits (e.g., `87654321.webp`)

## Dataframe Structure

The migration updates the `images.trademarks` structure:

```python
# Before
{
    "old_filename.webp": {
        "reg_no": ["1234567"],
        "int_cls_list": [35, 42],
        "trademark_text": ["BRAND NAME"],
        "full_filename": ["old_filename_full.webp"]
    }
}

# After  
{
    "87654321.webp": {
        "reg_no": ["1234567"],
        "ser_no": ["87654321"],  # Added
        "int_cls_list": [35, 42],
        "trademark_text": ["BRAND NAME"], 
        "full_filename": ["87654321_full.webp"]  # Updated
    }
}
```

## Error Handling

The migration script handles various error scenarios:

1. **Database Connection Issues**: Graceful error handling with detailed logging
2. **Missing Trademarks**: Uses LLM to identify missing reg_no/ser_no pairs
3. **LLM Failures**: Falls back to manual user input
4. **File Copy Failures**: Logs errors but continues processing other files
5. **User Skip Option**: Allows skipping problematic entries

## Manual Intervention

When the script cannot automatically determine the ser_no, it will:

1. Display the certificate URL for visual inspection
2. Show the current reg_no
3. Prompt for 8-digit ser_no input
4. Allow 'skip' option to bypass problematic entries

Example prompt:
```
============================================================
MANUAL INPUT REQUIRED
============================================================
Registration Number: 1234567
Certificate URL: http://troimages-1329604052.cos.ap-guangzhou.myqcloud.com/plaintiff_images/123/low/cert.webp
============================================================
Enter the 8-digit serial number (or 'skip' to skip this item): 
```

## Verification

After migration, verify:

1. **File Existence**: New ser_no based files exist in COS
2. **Database Updates**: Trademark records have `tro='TRUE'` and correct plaintiff_ids
3. **Dataframe Integrity**: Cases have updated trademark data with ser_no fields
4. **Filename Format**: All migrated files follow `{8-digit-ser_no}.webp` pattern

## Rollback

If rollback is needed:
1. Original files are preserved (copied, not moved)
2. Database changes can be reverted by setting `tro=NULL` and removing plaintiff_ids
3. Dataframe can be restored from backup or re-processed

## Notes

- Migration preserves original files (uses copy, not move)
- Only processes cases that haven't been migrated (8-digit filename check)
- Updates database records to mark trademarks as used in TRO cases
- Handles multiple reg_nos per trademark entry (uses first for filename)
- Maintains backward compatibility during transition period
