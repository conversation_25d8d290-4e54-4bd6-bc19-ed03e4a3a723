#!/usr/bin/env python3
"""
Test script to verify the dead/cancelled trademark check functionality.
"""

import os
import sys
import asyncio

sys.path.append(os.getcwd())

from IP.Trademarks.USPTO_TSDR_API import TSDRApi


async def test_dead_trademark_check():
    """Test the dead trademark check functionality"""
    
    # Initialize the API client
    api_client = TSDRApi()
    await api_client.start_session()
    
    try:
        # Test with a known registration number (you can replace this with a test reg_no)
        test_reg_no = "1234567"  # Replace with actual reg_no for testing
        
        print(f"Testing dead trademark check for reg_no: {test_reg_no}")
        
        # Get XML content
        xml_content = await api_client.get_status_info_xml(test_reg_no, id_key='rn')
        
        if xml_content:
            # Process XML content
            data = api_client.process_xml_content(xml_content)
            
            print(f"Processed data:")
            print(f"  reg_no: {data.get('reg_no')}")
            print(f"  ser_no: {data.get('ser_no')}")
            print(f"  status_code: {data.get('status_code')}")
            print(f"  text: {data.get('text')}")
            print(f"  applicant_name: {data.get('applicant_name')}")
            
            # Check if it's a dead trademark
            dead_status_codes = ["600", "601", "602", "603", "604", "605", "606", "607", "608", "609",
                               "614", "618", "620", "622", "626", "710", "711", "712", "713", "714", "900"]
            
            status_code = data.get("status_code")
            is_dead = status_code in dead_status_codes if status_code else False
            
            print(f"  is_dead: {is_dead}")
            
            if is_dead:
                print(f"✅ Trademark {test_reg_no} is dead/cancelled (status: {status_code})")
                print(f"   Would use filename: {data.get('ser_no')}_dead_full.webp")
            else:
                print(f"✅ Trademark {test_reg_no} is active (status: {status_code})")
                print(f"   Would use filename: {data.get('ser_no')}_full.webp")
                
        else:
            print(f"❌ Could not retrieve XML content for reg_no: {test_reg_no}")
            
    except Exception as e:
        print(f"❌ Error during test: {e}")
        
    finally:
        await api_client.close_session()


if __name__ == "__main__":
    print("Testing dead trademark check functionality...")
    print("=" * 50)
    asyncio.run(test_dead_trademark_check())
    print("=" * 50)
    print("Test completed!")
