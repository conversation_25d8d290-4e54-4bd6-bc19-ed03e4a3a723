import os
from AI.LLM_shared import get_json, get_report
from AI.GC_VertexAI import vertex_genai_multi_async


def get_report_prompt(ip_type, result):
    """Get appropriate report prompt based on IP type and parameters"""
    prompts_dir = os.path.join(os.getcwd(), "Check", "Prompts")
    
    if ip_type == "Patent":
        if "D" in result.get('reg_no', ''):
            prompt_file = "Report_Patent_Design.txt"
        else:
            prompt_file = "Report_Patent_Utility.txt"
    elif ip_type == "Copyright":
        prompt_file = "Report_Copyright.txt"
    elif ip_type == "Trademark":
        if "ip_local_paths" in result and len(result["ip_local_paths"]) > 0:
            prompt_file = "Report_Trademark_Images.txt"
        else:
            prompt_file = "Report_Trademark_Text.txt"
    else:
        raise ValueError(f"Unsupported IP type: {ip_type}")
    
    with open(os.path.join(prompts_dir, prompt_file), "r", encoding="utf-8") as f:
        return f.read()


def get_report_config(ip_type):
    """Get report configuration based on IP type"""
    configs = {
        "Patent": {
            "title": "**Patent Risk Assessment Report**",
            "start_marker": "**1. Patent Information:**",
            "end_marker": "**End of Report**"
        },
        "Copyright": {
            "title": "**Copyright Risk Assessment Report**",
            "start_marker": "**1. Image Information:**",
            "end_marker": "**End of Report**"
        },
        "Trademark": {
            "title": "**Trademark Risk Assessment Report**",
            "start_marker": "**1. Mark Information:**",
            "end_marker": "**End of Report**"
        }
    }
    return configs.get(ip_type, configs["Copyright"])



async def create_check_report(ip_type, check_id, result, client=None, bucket=None, model_name="gemini-2.0-flash-exp", description=None, keywords=None, reference_text=None):
    """
    Centralized function to create IP infringement reports
    
    Args:
        ip_type: Type of IP ("Patent", "Copyright", "Trademark")
        check_id: Check ID for URL generation
        result: Result dictionary containing IP information
        client: COS client for file operations
        bucket: COS bucket name
        model_name: Model name for AI generation
    
    Returns:
        Updated result dictionary with report, or None if no report needed
    """
    # Validate required data
    # if 'download_statuses' not in result or not all(result['download_statuses']):
    #     return None
    
    if not os.path.exists(result['product_local_path']) or any(not os.path.exists(ip_local_path) for ip_local_path in result['ip_local_paths']):
        return None
    
    # Get report configuration
    config = get_report_config(ip_type)
    report_prompt = get_report_prompt(ip_type, result)
    
    # Build prompt list based on IP type and query type
    prompt_list = [
        ("text", report_prompt),
        ("text", f'\n\nAfter the report, conclude with regards to the risk of infringement with {{"final_answer": "xx"}} where xx is a score between 0 and 10 where 0 is very low risk, and 10 is very high risk.'),
        ("text", "\n\nProduct Image:"),
        ("image_path", result["product_local_path"]),
    ]
    product_url = create_product_url(check_id, result['product_local_path'])
    image_url = ["", "", "", product_url.replace(" ", "%20").replace("http", "https")]
    
    # Add IP-specific content to prompt
    if ip_type == "Patent":
        registered_patent = "Registered Design Patent" if "D" in result['reg_no'] else "Registered Utility Patent"
        prompt_list.append(("text", f"\n\n{registered_patent} with registration number {result['reg_no']}:\n"))
        image_url.append("")
        
        # Add all patent images
        for ip_file_local, ip_url in zip(result['ip_local_paths'], result['ip_asset_urls']):
            prompt_list.append(("image_path", ip_file_local))
            image_url.append(ip_url.replace(" ", "%20").replace("http", "https"))

    elif ip_type == "Copyright":
        prompt_list.extend([
            ("text", f"\n\nCopyright Registered Image from '{result['ip_owner']}' with registration number '{result['reg_no']}':"),
            ("image_path", result['ip_local_paths'][0]),
        ])
        image_url.extend(["", result['ip_asset_urls'][0].replace(" ", "%20").replace("http", "https")])
        
    elif ip_type == "Trademark":
        if "ip_local_paths" in result and len(result["ip_local_paths"]) > 0:
            for ip_file_local, ip_url in zip(result['ip_local_paths'], result['ip_asset_urls']):
                prompt_list.append(("text", f"\n\nTrademark Registered Image with Registration Number {result['reg_no']} and International Classification {result.get('int_cls_list', '')}:"))
                prompt_list.append(("image_path", ip_file_local))
                image_url.extend(["", ip_url.replace(" ", "%20").replace("http", "https")])
        else:
            text_prompt = f"""
            **Product Information:**
            - Product Description: {description or "Not provided"}
            - Keywords: {keywords or "Not provided"}
            - Reference Text: {reference_text or "Not provided"}

            **Registered Trademarks to Analyze:**
            - Trademark Text:** {result['text']}
            - Owner:** {result['ip_owner']}
            - Registration Number:** {result.get('reg_no', 'Not available')}
            - International Classes:** {', '.join(result['int_cls_descriptions']) if result['int_cls_descriptions'] else 'Not available'}
            """
            
            prompt_list.append(("text", text_prompt))
            image_url.append("")
    
        
    # Get AI response
    ai_answer = await vertex_genai_multi_async(prompt_list, image_url=image_url, model_name=model_name)
    risk_assessment = get_json(ai_answer)
    
    try:
        final_answer_score = int(risk_assessment["final_answer"])
    except:
        final_answer_score = 0  # Default to 0 for "Report not required"
    
    if final_answer_score > 0:
        # Extract and format report
        report = get_report(ai_answer, config["start_marker"], config["end_marker"])
        report = config["title"] + "\n\n" + report
        
        # Update result with report information
        result["report"] = report
        result["risk_level"] = "高风险" if final_answer_score > 5 else "中风险" if final_answer_score > 2 else "低风险"
        result["risk_description"] = "和其他产权有匹配，并且该产权或产权所有人曾经发起TRO诉讼"
        result["product_url"] = product_url
        
        return result
    else:
        return None


def create_product_url(check_id, product_image_path):
    """Create product URL for COS storage"""
    product_url = f"https://tro-1330776830.cos.ap-guangzhou.myqcloud.com/checks/{check_id}/query/{os.path.basename(product_image_path)}"
    return product_url