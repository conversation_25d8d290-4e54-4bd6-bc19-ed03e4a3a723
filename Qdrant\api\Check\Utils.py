
import hashlib
import os

def generate_obfuscated_key(reg_no: str) -> str:
    """
    Generates an obfuscated key for an image filename using SHA256.
    
    Args:
        reg_no (str): The registration number of the IP asset.
        secret (bytes): A secret byte string used to salt the hash.
                        Defaults to _IMAGE_SECRET.
                        
    Returns:
        str: The SHA256 hexdigest of the combined reg_no and secret.
    """
    key_raw = reg_no.encode('utf-8') + os.getenv("IMAGE_SECRET").encode("utf-8")
    return hashlib.sha256(key_raw).hexdigest()


def create_ip_url(ip_type, reg_no):
    """Create IP URL with proper formatting"""
    obfuscated_reg_no = generate_obfuscated_key(reg_no)
    IP_Url = f"https://tro-1330776830.cos.ap-guangzhou.myqcloud.com/ip_assets/{ip_type}s/{obfuscated_reg_no}.webp"
    
    return IP_Url

def create_product_url(check_id, product_image_path):
    """Create product URL for COS storage"""
    # product_url = f"http://troimages-1329604052.cos.ap-guangzhou.myqcloud.com/checks/{check_id}/query/{os.path.basename(product_image_path)}"
    product_url = f"https://tro-1330776830.cos.ap-guangzhou.myqcloud.com/checks/{check_id}/query/{os.path.basename(product_image_path)}"
    return product_url