#!/usr/bin/env python3
"""
Setup script for PostgreSQL database schema.
This script initializes the PostgreSQL database with the required tables and triggers
for the IP Infringement Detection system.
"""

import os
import psycopg2
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

def setup_postgres():
    """
    Set up PostgreSQL database schema.
    """
    # Get PostgreSQL connection details from environment variables
    postgres_host = os.getenv("POSTGRES_HOST")
    postgres_port = os.getenv("POSTGRES_PORT")
    postgres_user = os.getenv("POSTGRES_USER")
    postgres_password = os.getenv("POSTGRES_PASSWORD")
    postgres_db = os.getenv("POSTGRES_DB")

    # Connect to PostgreSQL
    conn = psycopg2.connect(
        host=postgres_host,
        port=postgres_port,
        user=postgres_user,
        password=postgres_password,
        dbname=postgres_db
    )
    conn.autocommit = True
    cursor = conn.cursor()

    # Create UUID extension
    create_uuid_extension(cursor)

    # Create trigger function for automatic timestamp updates
    create_timestamp_trigger_function(cursor)

    # Create tables
    create_trademarks_table(cursor)
    create_patents_table(cursor)
    create_copyrights_table(cursor)
    create_reverse_check_result_table(cursor)

    # Close connection
    cursor.close()
    conn.close()

    print("PostgreSQL setup completed successfully.")

def create_uuid_extension(cursor):
    """
    Create UUID extension in PostgreSQL.
    """
    print("Creating UUID extension...")
    cursor.execute("CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\";")
    print("UUID extension created successfully.")

def create_timestamp_trigger_function(cursor):
    """
    Create trigger function for automatic timestamp updates.
    """
    print("Creating timestamp trigger function...")
    cursor.execute("""
    CREATE OR REPLACE FUNCTION trigger_set_timestamp()
    RETURNS TRIGGER AS $$
    BEGIN
      NEW.update_time = NOW();
      RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;
    """)
    print("Timestamp trigger function created successfully.")

def create_trademarks_table(cursor):
    """
    Create trademarks table in PostgreSQL.
    """
    print("Creating trademarks table...")
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS trademarks (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        reg_no TEXT,
        ser_no TEXT,
        TRO TEXT,
        applicant_name TEXT,
        mark_text TEXT,
        int_cls BIGINT[], -- Array of BigIntegers
        filing_date DATE, -- Renamed from 'date'
        plaintiff_ids BIGINT[], -- Array of BigIntegers
        nb_suits BIGINT,
        country_codes TEXT[], -- Array of TEXT
        associated_marks TEXT[], -- Array of TEXT
        info_source TEXT,
        image_source TEXT,
        certificate_source TEXT,
        mark_current_status_code INTEGER,
        mark_feature_code INTEGER,
        mark_standard_character_indicator BOOLEAN,
        mark_disclaimer_text TEXT[], -- Array of TEXT
        mark_disclaimer_text_daily TEXT[], -- For D0, D1. Store the <text> content
        mark_image_colour_claimed_text TEXT,
        mark_image_colour_part_claimed_text TEXT,
        mark_image_colour_statement_daily TEXT[], -- For CC, CD. Store the <text> content
        mark_translation_statement_daily TEXT, -- For TR. Store the <text> content
        name_portrait_statement_daily TEXT, -- For N0. Store the <text> content
        mark_description_statement_daily TEXT, -- For DM. Store the <text> content
        certification_mark_statement_daily TEXT, -- For CS. Store the <text> content
        lining_stippling_statement_daily TEXT, -- For LS. Store the <text> content
        section_2f_statement_daily TEXT, -- For TF. Store the <text> content
        national_design_code TEXT[], -- Array of TEXT
        goods_services JSONB, -- Store list of structured objects as JSONB
        goods_services_text_daily TEXT, -- For GS type codes. Stores raw text block
        case_file_statements_other JSONB, -- For other type codes (AF, A0, B0, CU, FN, IN, MD, MK, NR, OR, PM)
        mark_current_status_external_description_text TEXT,
        create_time TIMESTAMPTZ NOT NULL DEFAULT now(),
        update_time TIMESTAMPTZ NOT NULL DEFAULT now()
    );

    -- Indexes
    CREATE INDEX IF NOT EXISTS idx_trademarks_reg_no ON trademarks (reg_no);
    CREATE INDEX IF NOT EXISTS idx_trademarks_ser_no ON trademarks (ser_no);

    -- Trigger (Apply after table creation)
    DROP TRIGGER IF EXISTS set_timestamp ON trademarks; -- Ensure idempotency
    CREATE TRIGGER set_timestamp
    BEFORE UPDATE ON trademarks
    FOR EACH ROW
    EXECUTE FUNCTION trigger_set_timestamp();

    -- Add UNIQUE constraint to ser_no (idempotent)
    ALTER TABLE trademarks DROP CONSTRAINT IF EXISTS trademarks_ser_no_unique;
    ALTER TABLE trademarks ADD CONSTRAINT trademarks_ser_no_unique UNIQUE (ser_no);
    """)
    print("Trademarks table created successfully.")

def create_patents_table(cursor):
    """
    Create patents table in PostgreSQL.
    """
    print("Creating patents table...")
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS patents (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        reg_no TEXT,
        document_id TEXT,
        TRO TEXT,
        inventors TEXT,
        assignee TEXT,
        applicant TEXT,
        patent_title TEXT,
        date_published DATE,
        plaintiff_ids BIGINT[], -- Array of BigIntegers
        patent_type TEXT,
        abstract TEXT,
        associated_patents TEXT[], -- Array of TEXT
        design_page_numbers INTEGER[], -- Array of Integers
        pdf_source TEXT,
        image_source TEXT,
        certificate_source TEXT,
        patent_number TEXT,
        grant_date DATE,
        kind_code TEXT,
        application_number TEXT,
        cpc_section TEXT,
        cpc_class TEXT,
        cpc_subclass TEXT,
        cpc_main_group TEXT,
        cpc_subgroup TEXT,
        symbol_position_code TEXT,
        cpc_classification_value_code TEXT,
        classification_version_date DATE,
        create_time TIMESTAMPTZ NOT NULL DEFAULT now(),
        update_time TIMESTAMPTZ NOT NULL DEFAULT now()
    );

    -- Indexes
    CREATE INDEX IF NOT EXISTS idx_patents_reg_no ON patents (reg_no);
    CREATE INDEX IF NOT EXISTS idx_patents_document_id ON patents (document_id);

    -- Trigger (Apply after table creation)
    DROP TRIGGER IF EXISTS set_timestamp ON patents; -- Ensure idempotency
    CREATE TRIGGER set_timestamp
    BEFORE UPDATE ON patents
    FOR EACH ROW
    EXECUTE FUNCTION trigger_set_timestamp();
    """)
    print("Patents table created successfully.")

def create_copyrights_table(cursor):
    """
    Create copyrights table in PostgreSQL.
    """
    print("Creating copyrights table...")
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS copyrights (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        TRO TEXT,
        registration_number TEXT,
        registration_date DATE,
        type_of_work TEXT,
        title TEXT,
        date_of_creation INTEGER, -- Storing year only as INTEGER
        date_of_publication DATE,
        copyright_claimant TEXT, -- Consider TEXT[] if multiple structured claimants
        authorship_on_application TEXT,
        rights_and_permissions TEXT,
        description TEXT,
        nation_of_first_publication TEXT,
        names TEXT, -- Consider TEXT[] if multiple structured names
        plaintiff_ids BIGINT[], -- Array of BigIntegers for plaintiff IDs, matching patents and trademarks
        create_time TIMESTAMPTZ NOT NULL DEFAULT now(),
        update_time TIMESTAMPTZ NOT NULL DEFAULT now()
    );

    -- Indexes
    CREATE INDEX IF NOT EXISTS idx_copyrights_reg_no ON copyrights (registration_number);

    -- Trigger (Apply after table creation)
    DROP TRIGGER IF EXISTS set_timestamp ON copyrights; -- Ensure idempotency
    CREATE TRIGGER set_timestamp
    BEFORE UPDATE ON copyrights
    FOR EACH ROW
    EXECUTE FUNCTION trigger_set_timestamp();
    """)
    print("Copyrights table created successfully.")

def create_reverse_check_result_table(cursor):
    """
    Create reverse_check_result table in PostgreSQL.
    """
    print("Creating reverse_check_result table...")
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS reverse_check_result (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        date_added DATE,
        client_id TEXT,
        check_id BIGINT,
        results JSONB,
        create_time TIMESTAMPTZ NOT NULL DEFAULT now(),
        update_time TIMESTAMPTZ NOT NULL DEFAULT now()
    );

    -- Indexes
    CREATE INDEX IF NOT EXISTS idx_reverse_check_result_client_id ON reverse_check_result (client_id);
    CREATE INDEX IF NOT EXISTS idx_reverse_check_result_check_id ON reverse_check_result (check_id);

    -- Trigger (Apply after table creation)
    DROP TRIGGER IF EXISTS set_timestamp ON reverse_check_result; -- Ensure idempotency
    CREATE TRIGGER set_timestamp
    BEFORE UPDATE ON reverse_check_result
    FOR EACH ROW
    EXECUTE FUNCTION trigger_set_timestamp();
    """)
    print("Reverse check result table created successfully.")

if __name__ == "__main__":
    setup_postgres()
