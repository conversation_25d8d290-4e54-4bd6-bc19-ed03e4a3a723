# Api endpoint for the client to collect the reverse check results. 
# Table name: reverse_check_result
# Table structure: 
# id
# client_id
# check_id
# result: {check_id, risk_level, results=[still a list (aggregate hits by check_id)]}
# create_time
# update_time

from fastapi import APIRouter, Depends, Request
from fastapi.responses import JSONResponse
from utils.auth import verify_token
from utils.db import get_reverse_check_results_by_client_id_and_date
import sys
sys.path.append("/app/TRO-USside")
import json

router = APIRouter()

with open('api_keys.json', 'r') as f:
    allowed_api_keys = json.load(f)

@router.post("/reverse_check_status", dependencies=[Depends(verify_token)])
async def reverse_check_status(request: Request):
    data = await request.json()
    api_key = data.get("api_key")
    date = data.get("date")
    if not api_key or api_key not in allowed_api_keys:
        return JSONResponse(status_code=401, content={"error": "Invalid API Key. Authentication failed."})
    if not date:
        return JSONResponse(status_code=400, content={"error": "Missing required field: date"})
    client_id = allowed_api_keys[api_key]["id"] if isinstance(allowed_api_keys[api_key], dict) else allowed_api_keys[api_key]
    results = get_reverse_check_results_by_client_id_and_date(client_id, date)
    return JSONResponse(content={"results": results})