# Api endpoint for the client to collect the reverse check results. 
# Table name: reverse_check_result
# Table structure: 
# id
# client_id
# check_id
# result: {check_id, risk_level, results=[still a list (aggregate hits by check_id)]}
# create_time
# update_time

from fastapi import APIRouter, Depends, Request
from fastapi.responses import JSONResponse
from utils.auth import verify_token
from utils.db import get_reverse_check_results_by_client_id_and_date
import sys
sys.path.append("/app/TRO-USside")
import json

router = APIRouter()

with open('api_keys.json', 'r') as f:
    allowed_api_keys = json.load(f)

@router.post("/reverse_check_status", dependencies=[Depends(verify_token)])
async def reverse_check_status(request: Request):
    data = await request.json()
    api_key = data.get("api_key")
    date = data.get("date")
    start_date = data.get("start_date")
    end_date = data.get("end_date")

    if not api_key or api_key not in allowed_api_keys:
        return JSONResponse(status_code=401, content={"error": "Invalid API Key. Authentication failed."})

    # Support both single date and date range
    if date:
        # Legacy single date support
        client_id = allowed_api_keys[api_key]["id"] if isinstance(allowed_api_keys[api_key], dict) else allowed_api_keys[api_key]
        results = get_reverse_check_results_by_client_id_and_date(client_id, date)
        return JSONResponse(content={"results": results})
    elif start_date and end_date:
        # New date range support
        from datetime import datetime, timedelta
        try:
            start_dt = datetime.strptime(start_date, '%Y-%m-%d').date()
            end_dt = datetime.strptime(end_date, '%Y-%m-%d').date()

            # Validate date range (maximum 1 month)
            if (end_dt - start_dt).days > 31:
                return JSONResponse(status_code=400, content={"error": "Date range cannot exceed 1 month (31 days)"})

            if start_dt > end_dt:
                return JSONResponse(status_code=400, content={"error": "Start date must be before or equal to end date"})

            client_id = allowed_api_keys[api_key]["id"] if isinstance(allowed_api_keys[api_key], dict) else allowed_api_keys[api_key]
            results = get_reverse_check_results_by_client_id_and_date_range(client_id, start_date, end_date)

            # Group results by date and check_id for the frontend
            grouped_results = {}
            for result in results:
                date_key = result['date_added'].strftime('%Y-%m-%d') if hasattr(result['date_added'], 'strftime') else str(result['date_added'])
                if date_key not in grouped_results:
                    grouped_results[date_key] = {}
                check_id = str(result['check_id'])
                if check_id not in grouped_results[date_key]:
                    grouped_results[date_key][check_id] = []
                grouped_results[date_key][check_id].append(result)

            return JSONResponse(content={"results": grouped_results})
        except ValueError as e:
            return JSONResponse(status_code=400, content={"error": f"Invalid date format. Use YYYY-MM-DD: {str(e)}"})
    else:
        return JSONResponse(status_code=400, content={"error": "Missing required field: either 'date' or both 'start_date' and 'end_date'"})