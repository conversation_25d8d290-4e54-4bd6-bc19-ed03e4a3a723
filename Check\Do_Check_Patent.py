from langfuse import observe
import time
import asyncio

from Check.RAG.qdrant_search import find_similar_assets_qdrant
from Check.Create_Report import create_check_report

@observe()
async def check_patents(client, bucket, temp_dir, client_name, check_id, local_product_images, local_client_ip_images, local_reference_images, description, ip_keywords, reference_text, cases_df, plaintiff_df):

    start_time = time.time()

    sim_results = await find_similar_assets_qdrant(
        query_image_paths=local_product_images+local_client_ip_images+local_reference_images,
        check_id=check_id,
        client_id=client_name,
        ip_type="Patent",
        temp_dir=temp_dir,
        cases_df=cases_df,
        plaintiff_df=plaintiff_df,
        plaintiff_id=None,
        top_n=5,
        similarity_threshold=0.6,
        similarity_threshold_text=0.25
    )

    print(f"\033[94mPatent: Patent RAG for {len(local_product_images)} pictures done in {time.time() - start_time:.1f} seconds\033[0m")

    if sim_results:
        # Concurrently process each patent check
        patent_check_tasks = [create_check_report(
            ip_type="Patent",
            check_id=check_id,
            result=result,
            client=client,
            bucket=bucket,
            model_name="gemini-2.0-flash-exp"
        ) for result in sim_results]
        patent_results = await asyncio.gather(*patent_check_tasks)

        # Filter out None results (where is_copyright["final_answer"] was not "yes")
        filtered_patent_results = [r for r in patent_results if r]
        print(f"\033[94m ✅ Patent: Patent Report Analysis DONE, for {len(sim_results)} results in {time.time() - start_time:.1f} seconds\033[0m")
        return filtered_patent_results # not used, but tracked by langfuse
    else:
        print(f"\033[94m ✅ Patent: RAG returned no pottential patent matches in {time.time() - start_time:.1f} seconds\033[0m")
        return []
